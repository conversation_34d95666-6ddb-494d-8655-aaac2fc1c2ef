<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'Dashboard' ?> - Fingerspot Management</title>
    
    <!-- Favicon -->
    <link rel="shortcut icon" type="image/png" href="/favicon.ico">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body { font-family: 'Inter', sans-serif; }
        .sidebar-transition { transition: all 0.3s ease-in-out; }
        .card-hover { transition: all 0.2s ease-in-out; }
        .card-hover:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0,0,0,0.1); }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .status-online { color: #10b981; }
        .status-offline { color: #ef4444; }
        .status-warning { color: #f59e0b; }
        .loading-spinner { animation: spin 1s linear infinite; }
        @keyframes spin { from { transform: rotate(0deg); } to { transform: rotate(360deg); } }
        .fade-in { animation: fadeIn 0.5s ease-in; }
        @keyframes fadeIn { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } }
    </style>
</head>
<body class="bg-gray-50 dark:bg-gray-900">
    <!-- Sidebar -->
    <div id="sidebar" class="fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg sidebar-transition">
        <div class="flex items-center justify-between h-16 bg-gradient-to-r from-blue-600 to-purple-600 px-4">
            <h1 id="sidebar-title" class="text-xl font-bold text-white">
                <i class="fas fa-fingerprint mr-2"></i>
                <span class="sidebar-text">Fingerspot</span>
            </h1>
            <button id="sidebar-collapse" class="text-white hover:text-gray-200 transition-colors duration-200 hidden lg:block">
                <i class="fas fa-chevron-left"></i>
            </button>
        </div>
        
        <nav class="mt-8">
            <div class="px-4 space-y-1">
                <!-- Dashboard -->
                <a href="/dashboard" class="nav-item <?= ($page ?? '') === 'dashboard' ? 'active' : '' ?>" data-page="dashboard">
                    <i class="fas fa-tachometer-alt w-5"></i>
                    <span>Dashboard</span>
                </a>

                <!-- Attendance Management -->
                <div class="nav-group">
                    <button class="nav-item nav-toggle <?= in_array(($page ?? ''), ['attendance', 'attendance-logs', 'attendance-reports']) ? 'active' : '' ?>" data-target="attendance-menu">
                        <div class="flex items-center flex-1">
                            <i class="fas fa-clock w-5 mr-3"></i>
                            <span>Attendance</span>
                        </div>
                        <i class="fas fa-chevron-right nav-arrow transition-transform duration-200"></i>
                    </button>
                    <div id="attendance-menu" class="nav-submenu <?= in_array(($page ?? ''), ['attendance', 'attendance-logs', 'attendance-reports']) ? 'show' : '' ?>">
                        <a href="/dashboard/attendance" class="nav-subitem <?= ($page ?? '') === 'attendance' ? 'active' : '' ?>">
                            <i class="fas fa-list w-4"></i>
                            <span>View Logs</span>
                        </a>
                        <a href="/dashboard/attendance/real-time" class="nav-subitem <?= ($page ?? '') === 'attendance-realtime' ? 'active' : '' ?>">
                            <i class="fas fa-broadcast-tower w-4"></i>
                            <span>Real-time Monitor</span>
                            <span id="realtime-badge" class="nav-badge hidden">0</span>
                        </a>
                        <a href="/dashboard/attendance/reports" class="nav-subitem <?= ($page ?? '') === 'attendance-reports' ? 'active' : '' ?>">
                            <i class="fas fa-chart-line w-4"></i>
                            <span>Attendance Reports</span>
                        </a>
                    </div>
                </div>

                <!-- User Management -->
                <div class="nav-group">
                    <button class="nav-item nav-toggle <?= in_array(($page ?? ''), ['users', 'user-groups', 'user-permissions']) ? 'active' : '' ?>" data-target="users-menu">
                        <div class="flex items-center flex-1">
                            <i class="fas fa-users w-5 mr-3"></i>
                            <span>Users</span>
                        </div>
                        <i class="fas fa-chevron-right nav-arrow transition-transform duration-200"></i>
                    </button>
                    <div id="users-menu" class="nav-submenu <?= in_array(($page ?? ''), ['users', 'user-groups', 'user-permissions']) ? 'show' : '' ?>">
                        <a href="/dashboard/users" class="nav-subitem <?= ($page ?? '') === 'users' ? 'active' : '' ?>">
                            <i class="fas fa-user w-4"></i>
                            <span>Manage Users</span>
                        </a>
                        <a href="/dashboard/users/groups" class="nav-subitem <?= ($page ?? '') === 'user-groups' ? 'active' : '' ?>">
                            <i class="fas fa-users-cog w-4"></i>
                            <span>User Groups</span>
                        </a>
                        <a href="/dashboard/users/biometric" class="nav-subitem <?= ($page ?? '') === 'user-biometric' ? 'active' : '' ?>">
                            <i class="fas fa-fingerprint w-4"></i>
                            <span>Biometric Setup</span>
                        </a>
                        <a href="/dashboard/users/bulk-import" class="nav-subitem <?= ($page ?? '') === 'user-import' ? 'active' : '' ?>">
                            <i class="fas fa-upload w-4"></i>
                            <span>Bulk Import</span>
                        </a>
                    </div>
                </div>

                <!-- Device Management -->
                <div class="nav-group">
                    <button class="nav-item nav-toggle <?= in_array(($page ?? ''), ['devices', 'device-config', 'device-maintenance']) ? 'active' : '' ?>" data-target="devices-menu">
                        <div class="flex items-center flex-1">
                            <i class="fas fa-microchip w-5 mr-3"></i>
                            <span>Devices</span>
                        </div>
                        <i class="fas fa-chevron-right nav-arrow transition-transform duration-200"></i>
                    </button>
                    <div id="devices-menu" class="nav-submenu <?= in_array(($page ?? ''), ['devices', 'device-config', 'device-maintenance']) ? 'show' : '' ?>">
                        <a href="/dashboard/devices" class="nav-subitem <?= ($page ?? '') === 'devices' ? 'active' : '' ?>">
                            <i class="fas fa-desktop w-4"></i>
                            <span>Device Status</span>
                        </a>
                        <a href="/dashboard/devices/configuration" class="nav-subitem <?= ($page ?? '') === 'device-config' ? 'active' : '' ?>">
                            <i class="fas fa-cogs w-4"></i>
                            <span>Configuration</span>
                        </a>
                        <a href="/dashboard/devices/maintenance" class="nav-subitem <?= ($page ?? '') === 'device-maintenance' ? 'active' : '' ?>">
                            <i class="fas fa-tools w-4"></i>
                            <span>Maintenance</span>
                        </a>
                        <a href="/dashboard/devices/firmware" class="nav-subitem <?= ($page ?? '') === 'device-firmware' ? 'active' : '' ?>">
                            <i class="fas fa-download w-4"></i>
                            <span>Firmware Update</span>
                        </a>
                    </div>
                </div>

                <!-- Reports & Analytics -->
                <div class="nav-group">
                    <button class="nav-item nav-toggle <?= in_array(($page ?? ''), ['reports', 'analytics', 'exports']) ? 'active' : '' ?>" data-target="reports-menu">
                        <div class="flex items-center flex-1">
                            <i class="fas fa-chart-bar w-5 mr-3"></i>
                            <span>Reports</span>
                        </div>
                        <i class="fas fa-chevron-right nav-arrow transition-transform duration-200"></i>
                    </button>
                    <div id="reports-menu" class="nav-submenu <?= in_array(($page ?? ''), ['reports', 'analytics', 'exports']) ? 'show' : '' ?>">
                        <a href="/dashboard/reports" class="nav-subitem <?= ($page ?? '') === 'reports' ? 'active' : '' ?>">
                            <i class="fas fa-file-alt w-4"></i>
                            <span>Standard Reports</span>
                        </a>
                        <a href="/dashboard/reports/custom" class="nav-subitem <?= ($page ?? '') === 'custom-reports' ? 'active' : '' ?>">
                            <i class="fas fa-edit w-4"></i>
                            <span>Custom Reports</span>
                        </a>
                        <a href="/dashboard/reports/analytics" class="nav-subitem <?= ($page ?? '') === 'analytics' ? 'active' : '' ?>">
                            <i class="fas fa-chart-pie w-4"></i>
                            <span>Analytics</span>
                        </a>
                        <a href="/dashboard/reports/exports" class="nav-subitem <?= ($page ?? '') === 'exports' ? 'active' : '' ?>">
                            <i class="fas fa-download w-4"></i>
                            <span>Export Data</span>
                        </a>
                    </div>
                </div>

                <!-- API & Integration -->
                <div class="nav-group">
                    <button class="nav-item nav-toggle <?= in_array(($page ?? ''), ['api', 'webhooks', 'integrations']) ? 'active' : '' ?>" data-target="api-menu">
                        <div class="flex items-center flex-1">
                            <i class="fas fa-plug w-5 mr-3"></i>
                            <span>API & Integration</span>
                        </div>
                        <i class="fas fa-chevron-right nav-arrow transition-transform duration-200"></i>
                    </button>
                    <div id="api-menu" class="nav-submenu <?= in_array(($page ?? ''), ['api', 'webhooks', 'integrations']) ? 'show' : '' ?>">
                        <a href="/dashboard/api/endpoints" class="nav-subitem <?= ($page ?? '') === 'api-endpoints' ? 'active' : '' ?>">
                            <i class="fas fa-code w-4"></i>
                            <span>API Endpoints</span>
                        </a>
                        <a href="/dashboard/api/webhooks" class="nav-subitem <?= ($page ?? '') === 'webhooks' ? 'active' : '' ?>">
                            <i class="fas fa-webhook w-4"></i>
                            <span>Webhooks</span>
                        </a>
                        <a href="/dashboard/api/logs" class="nav-subitem <?= ($page ?? '') === 'api-logs' ? 'active' : '' ?>">
                            <i class="fas fa-history w-4"></i>
                            <span>API Logs</span>
                            <span id="api-logs-badge" class="nav-badge hidden">0</span>
                        </a>
                    </div>
                </div>

                <!-- Settings -->
                <div class="nav-group">
                    <button class="nav-item nav-toggle <?= in_array(($page ?? ''), ['settings', 'system-config', 'security']) ? 'active' : '' ?>" data-target="settings-menu">
                        <div class="flex items-center flex-1">
                            <i class="fas fa-cog w-5 mr-3"></i>
                            <span>Settings</span>
                        </div>
                        <i class="fas fa-chevron-right nav-arrow transition-transform duration-200"></i>
                    </button>
                    <div id="settings-menu" class="nav-submenu <?= in_array(($page ?? ''), ['settings', 'system-config', 'security']) ? 'show' : '' ?>">
                        <a href="/dashboard/settings" class="nav-subitem <?= ($page ?? '') === 'settings' ? 'active' : '' ?>">
                            <i class="fas fa-sliders-h w-4"></i>
                            <span>General Settings</span>
                        </a>
                        <a href="/dashboard/settings/system" class="nav-subitem <?= ($page ?? '') === 'system-config' ? 'active' : '' ?>">
                            <i class="fas fa-server w-4"></i>
                            <span>System Config</span>
                        </a>
                        <a href="/dashboard/settings/security" class="nav-subitem <?= ($page ?? '') === 'security' ? 'active' : '' ?>">
                            <i class="fas fa-shield-alt w-4"></i>
                            <span>Security</span>
                        </a>
                        <a href="/dashboard/settings/backup" class="nav-subitem <?= ($page ?? '') === 'backup' ? 'active' : '' ?>">
                            <i class="fas fa-database w-4"></i>
                            <span>Backup & Restore</span>
                        </a>
                    </div>
                </div>
            </div>
        </nav>
        
        <div class="absolute bottom-4 left-4 right-4">
            <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-3">
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-300">Device Status</span>
                    <span id="device-status-indicator" class="flex items-center">
                        <i class="fas fa-circle text-xs status-offline mr-1"></i>
                        <span class="text-xs">Checking...</span>
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="ml-64 min-h-screen">
        <!-- Top Bar -->
        <header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between px-6 py-4">
                <div class="flex items-center">
                    <button id="sidebar-toggle" class="lg:hidden text-gray-500 hover:text-gray-700 mr-4">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-white"><?= $title ?? 'Dashboard' ?></h2>
                </div>
                
                <div class="flex items-center space-x-4">
                    <!-- Theme Toggle -->
                    <button id="theme-toggle" class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                        <i class="fas fa-moon dark:hidden"></i>
                        <i class="fas fa-sun hidden dark:inline"></i>
                    </button>
                    
                    <!-- Refresh Button -->
                    <button id="refresh-data" class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    
                    <!-- Notifications -->
                    <div class="relative">
                        <button class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                            <i class="fas fa-bell"></i>
                            <span id="notification-badge" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center hidden">0</span>
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Page Content -->
        <main class="p-6">
            <?= $this->renderSection('content') ?>
        </main>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 flex items-center space-x-3">
            <i class="fas fa-spinner loading-spinner text-blue-600"></i>
            <span class="text-gray-700 dark:text-gray-300">Loading...</span>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Global dashboard utilities
        const Dashboard = {
            // Show loading overlay
            showLoading() {
                document.getElementById('loading-overlay').classList.remove('hidden');
            },
            
            // Hide loading overlay
            hideLoading() {
                document.getElementById('loading-overlay').classList.add('hidden');
            },
            
            // Show toast notification
            showToast(message, type = 'info') {
                const toast = document.createElement('div');
                const bgColor = {
                    'success': 'bg-green-500',
                    'error': 'bg-red-500',
                    'warning': 'bg-yellow-500',
                    'info': 'bg-blue-500'
                }[type] || 'bg-blue-500';
                
                toast.className = `${bgColor} text-white px-6 py-3 rounded-lg shadow-lg fade-in`;
                toast.innerHTML = `
                    <div class="flex items-center justify-between">
                        <span>${message}</span>
                        <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;
                
                document.getElementById('toast-container').appendChild(toast);
                
                // Auto remove after 5 seconds
                setTimeout(() => {
                    if (toast.parentElement) {
                        toast.remove();
                    }
                }, 5000);
            },
            
            // Update device status indicator
            updateDeviceStatus(status) {
                const indicator = document.getElementById('device-status-indicator');
                const icon = indicator.querySelector('i');
                const text = indicator.querySelector('span');
                
                icon.className = 'fas fa-circle text-xs mr-1 ' + 
                    (status === 'online' ? 'status-online' : 
                     status === 'offline' ? 'status-offline' : 'status-warning');
                text.textContent = status.charAt(0).toUpperCase() + status.slice(1);
            }
        };

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            // Theme toggle
            const themeToggle = document.getElementById('theme-toggle');
            const html = document.documentElement;

            themeToggle.addEventListener('click', function() {
                html.classList.toggle('dark');
                localStorage.setItem('theme', html.classList.contains('dark') ? 'dark' : 'light');
            });

            // Load saved theme
            if (localStorage.getItem('theme') === 'dark') {
                html.classList.add('dark');
            }

            // Initialize multi-level navigation
            initializeNavigation();

            // Initialize sidebar collapse
            initializeSidebarCollapse();

            // Sidebar toggle for mobile
            const sidebarToggle = document.getElementById('sidebar-toggle');
            const sidebar = document.getElementById('sidebar');

            sidebarToggle?.addEventListener('click', function() {
                sidebar.classList.toggle('show');
            });

            // Close sidebar when clicking outside on mobile
            document.addEventListener('click', function(e) {
                if (window.innerWidth <= 1024) {
                    if (!sidebar.contains(e.target) && !sidebarToggle?.contains(e.target)) {
                        sidebar.classList.remove('show');
                    }
                }
            });

            // Refresh data button
            document.getElementById('refresh-data').addEventListener('click', function() {
                if (typeof refreshDashboardData === 'function') {
                    refreshDashboardData();
                } else {
                    location.reload();
                }
            });

            // Load initial device status
            fetch('/dashboard/api/device-status')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Dashboard.updateDeviceStatus(data.data.status);
                    }
                })
                .catch(() => {
                    Dashboard.updateDeviceStatus('offline');
                });
        });

        // Multi-level Navigation System
        function initializeNavigation() {
            // Handle menu toggle clicks
            const navToggles = document.querySelectorAll('.nav-toggle');

            navToggles.forEach(toggle => {
                toggle.addEventListener('click', function(e) {
                    e.preventDefault();

                    const targetId = this.getAttribute('data-target');
                    const submenu = document.getElementById(targetId);
                    const arrow = this.querySelector('.nav-arrow');

                    if (submenu) {
                        // Toggle current submenu
                        const isOpen = submenu.classList.contains('show');

                        if (isOpen) {
                            // Close submenu
                            submenu.classList.remove('show');
                            arrow.classList.remove('rotate-90');
                            this.classList.remove('active');
                        } else {
                            // Close other open submenus (optional - remove for accordion behavior)
                            closeAllSubmenus();

                            // Open current submenu
                            submenu.classList.add('show');
                            arrow.classList.add('rotate-90');
                            this.classList.add('active');
                        }

                        // Save menu state
                        saveMenuState();
                    }
                });
            });

            // Handle submenu item clicks
            const subItems = document.querySelectorAll('.nav-subitem');
            subItems.forEach(item => {
                item.addEventListener('click', function() {
                    // Remove active class from all subitems
                    subItems.forEach(si => si.classList.remove('active'));
                    // Add active class to clicked item
                    this.classList.add('active');

                    // Close sidebar on mobile after navigation
                    if (window.innerWidth <= 1024) {
                        document.getElementById('sidebar').classList.remove('show');
                    }
                });
            });

            // Restore menu state from localStorage
            restoreMenuState();
        }

        function closeAllSubmenus() {
            const submenus = document.querySelectorAll('.nav-submenu');
            const arrows = document.querySelectorAll('.nav-arrow');
            const toggles = document.querySelectorAll('.nav-toggle');

            submenus.forEach(submenu => submenu.classList.remove('show'));
            arrows.forEach(arrow => arrow.classList.remove('rotate-90'));
            toggles.forEach(toggle => toggle.classList.remove('active'));
        }

        function saveMenuState() {
            const openMenus = [];
            const submenus = document.querySelectorAll('.nav-submenu.show');

            submenus.forEach(submenu => {
                openMenus.push(submenu.id);
            });

            localStorage.setItem('openMenus', JSON.stringify(openMenus));
        }

        function restoreMenuState() {
            const openMenus = JSON.parse(localStorage.getItem('openMenus') || '[]');

            openMenus.forEach(menuId => {
                const submenu = document.getElementById(menuId);
                if (submenu) {
                    const toggle = document.querySelector(`[data-target="${menuId}"]`);
                    const arrow = toggle?.querySelector('.nav-arrow');

                    submenu.classList.add('show');
                    toggle?.classList.add('active');
                    arrow?.classList.add('rotate-90');
                }
            });
        }

        // Handle window resize
        window.addEventListener('resize', function() {
            const sidebar = document.getElementById('sidebar');

            if (window.innerWidth > 1024) {
                // Desktop: ensure sidebar is visible
                sidebar.classList.remove('show');
            }
        });

        // Sidebar Collapse Functionality
        function initializeSidebarCollapse() {
            const collapseBtn = document.getElementById('sidebar-collapse');
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.querySelector('.ml-64');

            collapseBtn?.addEventListener('click', function() {
                const isCollapsed = sidebar.classList.contains('collapsed');

                if (isCollapsed) {
                    // Expand sidebar
                    sidebar.classList.remove('collapsed');
                    mainContent?.classList.remove('ml-16');
                    mainContent?.classList.add('ml-64');
                    this.innerHTML = '<i class="fas fa-chevron-left"></i>';
                    localStorage.setItem('sidebarCollapsed', 'false');
                } else {
                    // Collapse sidebar
                    sidebar.classList.add('collapsed');
                    mainContent?.classList.remove('ml-64');
                    mainContent?.classList.add('ml-16');
                    this.innerHTML = '<i class="fas fa-chevron-right"></i>';
                    localStorage.setItem('sidebarCollapsed', 'true');

                    // Close all submenus when collapsing
                    closeAllSubmenus();
                }
            });

            // Restore collapse state
            if (localStorage.getItem('sidebarCollapsed') === 'true') {
                sidebar.classList.add('collapsed');
                mainContent?.classList.remove('ml-64');
                mainContent?.classList.add('ml-16');
                collapseBtn.innerHTML = '<i class="fas fa-chevron-right"></i>';
            }
        }

        // Update notification badges
        function updateNotificationBadges() {
            // Simulate real-time updates
            const realtimeBadge = document.getElementById('realtime-badge');
            const apiLogsBadge = document.getElementById('api-logs-badge');

            // Update real-time attendance badge
            fetch('/dashboard/api/recent-logs')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.data.data) {
                        const count = data.data.data.length;
                        if (count > 0) {
                            realtimeBadge.textContent = count;
                            realtimeBadge.classList.remove('hidden');
                        } else {
                            realtimeBadge.classList.add('hidden');
                        }
                    }
                })
                .catch(() => {
                    realtimeBadge.classList.add('hidden');
                });

            // Update API logs badge (simulate)
            const apiLogCount = Math.floor(Math.random() * 5);
            if (apiLogCount > 0) {
                apiLogsBadge.textContent = apiLogCount;
                apiLogsBadge.classList.remove('hidden');
            } else {
                apiLogsBadge.classList.add('hidden');
            }
        }

        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            // ESC key closes sidebar on mobile
            if (e.key === 'Escape' && window.innerWidth <= 1024) {
                document.getElementById('sidebar').classList.remove('show');
            }

            // Ctrl+B toggles sidebar collapse on desktop
            if (e.ctrlKey && e.key === 'b' && window.innerWidth > 1024) {
                e.preventDefault();
                document.getElementById('sidebar-collapse')?.click();
            }
        });

        // Update badges periodically
        setInterval(updateNotificationBadges, 30000); // Every 30 seconds
        updateNotificationBadges(); // Initial load
    </script>
    
    <style>
        /* Navigation Styles */
        .nav-item {
            @apply flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200 cursor-pointer;
        }
        .nav-item.active {
            @apply bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300;
        }
        .nav-item i {
            @apply mr-3;
        }

        /* Multi-level Menu Styles */
        .nav-group {
            @apply mb-1;
        }

        .nav-toggle {
            @apply w-full justify-between;
        }

        .nav-toggle.active .nav-arrow {
            @apply transform rotate-90;
        }

        .nav-submenu {
            @apply ml-4 mt-1 space-y-1 overflow-hidden transition-all duration-300 ease-in-out;
            max-height: 0;
            opacity: 0;
        }

        .nav-submenu.show {
            max-height: 500px;
            opacity: 1;
        }

        .nav-subitem {
            @apply flex items-center px-4 py-2 text-sm text-gray-600 dark:text-gray-400 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200;
        }

        .nav-subitem.active {
            @apply bg-blue-50 dark:bg-blue-900/50 text-blue-600 dark:text-blue-400;
        }

        .nav-subitem i {
            @apply mr-3;
        }

        /* Mobile Responsive */
        @media (max-width: 1024px) {
            #sidebar {
                @apply transform -translate-x-full;
            }

            #sidebar.show {
                @apply transform translate-x-0;
            }

            .nav-submenu.show {
                max-height: 300px;
            }
        }

        /* Sidebar Animation */
        .sidebar-transition {
            transition: transform 0.3s ease-in-out;
        }

        /* Menu Badge */
        .nav-badge {
            @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300 ml-auto;
        }

        /* Tooltip for collapsed sidebar */
        .nav-tooltip {
            @apply absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 pointer-events-none transition-opacity duration-200;
            top: 50%;
            transform: translateY(-50%);
        }

        .nav-item:hover .nav-tooltip {
            @apply opacity-100;
        }
    </style>
</body>
</html>
