<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'Dashboard' ?> - Fingerspot Management</title>
    
    <!-- Favicon -->
    <link rel="shortcut icon" type="image/png" href="/favicon.ico">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body { font-family: 'Inter', sans-serif; }
        .sidebar-transition { transition: all 0.3s ease-in-out; }
        .card-hover { transition: all 0.2s ease-in-out; }
        .card-hover:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0,0,0,0.1); }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .status-online { color: #10b981; }
        .status-offline { color: #ef4444; }
        .status-warning { color: #f59e0b; }
        .loading-spinner { animation: spin 1s linear infinite; }
        @keyframes spin { from { transform: rotate(0deg); } to { transform: rotate(360deg); } }
        .fade-in { animation: fadeIn 0.5s ease-in; }
        @keyframes fadeIn { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } }
    </style>
</head>
<body class="bg-gray-50 dark:bg-gray-900">
    <!-- Sidebar -->
    <div id="sidebar" class="fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-xl border-r border-gray-200 dark:border-gray-700 sidebar-transition">
        <!-- Sidebar Header -->
        <div class="flex items-center justify-between h-16 px-4 bg-gradient-to-r from-blue-600 to-purple-600">
            <div class="flex items-center">
                <div class="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-fingerprint text-white text-lg"></i>
                </div>
                <h1 class="text-xl font-bold text-white">Fingerspot</h1>
            </div>
            <button id="sidebar-toggle-mobile" class="lg:hidden text-white hover:text-gray-200">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <!-- Navigation Menu -->
        <nav class="flex-1 px-4 py-6 overflow-y-auto">
            <div class="space-y-1">
                <!-- Dashboard -->
                <a href="/dashboard" class="nav-item <?= ($page ?? '') === 'dashboard' ? 'active' : '' ?>">
                    <div class="flex items-center">
                        <div class="nav-icon">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <span class="nav-text">Dashboard</span>
                    </div>
                </a>

                <!-- Attendance Management -->
                <div class="nav-group">
                    <button type="button" class="nav-toggle <?= in_array(($page ?? ''), ['attendance']) ? 'active expanded' : '' ?>" data-target="attendance-submenu">
                        <div class="flex items-center flex-1">
                            <div class="nav-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <span class="nav-text">Attendance</span>
                        </div>
                        <div class="nav-arrow">
                            <i class="fas fa-chevron-down transform transition-transform duration-200 <?= in_array(($page ?? ''), ['attendance']) ? 'rotate-180' : '' ?>"></i>
                        </div>
                    </button>
                    <div id="attendance-submenu" class="nav-submenu <?= in_array(($page ?? ''), ['attendance']) ? 'expanded' : '' ?>">
                        <div class="nav-submenu-content">
                            <a href="/dashboard/attendance" class="nav-subitem <?= ($page ?? '') === 'attendance' ? 'active' : '' ?>">
                                <div class="nav-subitem-icon">
                                    <i class="fas fa-list"></i>
                                </div>
                                <span>View Logs</span>
                            </a>
                            <a href="/dashboard/attendance/real-time" class="nav-subitem">
                                <div class="nav-subitem-icon">
                                    <i class="fas fa-broadcast-tower"></i>
                                </div>
                                <span>Real-time Monitor</span>
                                <span class="nav-badge bg-green-500">Live</span>
                            </a>
                            <a href="/dashboard/attendance/export" class="nav-subitem">
                                <div class="nav-subitem-icon">
                                    <i class="fas fa-download"></i>
                                </div>
                                <span>Export Data</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- User Management -->
                <div class="nav-group">
                    <button type="button" class="nav-toggle <?= in_array(($page ?? ''), ['users']) ? 'active expanded' : '' ?>" data-target="users-submenu">
                        <div class="flex items-center flex-1">
                            <div class="nav-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <span class="nav-text">Users</span>
                        </div>
                        <div class="nav-arrow">
                            <i class="fas fa-chevron-down transform transition-transform duration-200 <?= in_array(($page ?? ''), ['users']) ? 'rotate-180' : '' ?>"></i>
                        </div>
                    </button>
                    <div id="users-submenu" class="nav-submenu <?= in_array(($page ?? ''), ['users']) ? 'expanded' : '' ?>">
                        <div class="nav-submenu-content">
                            <a href="/dashboard/users" class="nav-subitem <?= ($page ?? '') === 'users' ? 'active' : '' ?>">
                                <div class="nav-subitem-icon">
                                    <i class="fas fa-user-friends"></i>
                                </div>
                                <span>Manage Users</span>
                            </a>
                            <a href="/dashboard/users/add" class="nav-subitem">
                                <div class="nav-subitem-icon">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                                <span>Add User</span>
                            </a>
                            <a href="/dashboard/users/biometric" class="nav-subitem">
                                <div class="nav-subitem-icon">
                                    <i class="fas fa-fingerprint"></i>
                                </div>
                                <span>Biometric Setup</span>
                            </a>
                            <a href="/dashboard/users/import" class="nav-subitem">
                                <div class="nav-subitem-icon">
                                    <i class="fas fa-upload"></i>
                                </div>
                                <span>Bulk Import</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Device Management -->
                <div class="nav-group">
                    <button type="button" class="nav-toggle <?= in_array(($page ?? ''), ['devices']) ? 'active expanded' : '' ?>" data-target="devices-submenu">
                        <div class="flex items-center flex-1">
                            <div class="nav-icon">
                                <i class="fas fa-microchip"></i>
                            </div>
                            <span class="nav-text">Devices</span>
                        </div>
                        <div class="nav-arrow">
                            <i class="fas fa-chevron-down transform transition-transform duration-200 <?= in_array(($page ?? ''), ['devices']) ? 'rotate-180' : '' ?>"></i>
                        </div>
                    </button>
                    <div id="devices-submenu" class="nav-submenu <?= in_array(($page ?? ''), ['devices']) ? 'expanded' : '' ?>">
                        <div class="nav-submenu-content">
                            <a href="/dashboard/devices" class="nav-subitem <?= ($page ?? '') === 'devices' ? 'active' : '' ?>">
                                <div class="nav-subitem-icon">
                                    <i class="fas fa-desktop"></i>
                                </div>
                                <span>Device Status</span>
                                <span class="nav-badge bg-green-500">Online</span>
                            </a>
                            <a href="/dashboard/devices/configuration" class="nav-subitem">
                                <div class="nav-subitem-icon">
                                    <i class="fas fa-cogs"></i>
                                </div>
                                <span>Configuration</span>
                            </a>
                            <a href="/dashboard/devices/maintenance" class="nav-subitem">
                                <div class="nav-subitem-icon">
                                    <i class="fas fa-tools"></i>
                                </div>
                                <span>Maintenance</span>
                            </a>
                            <a href="/dashboard/devices/logs" class="nav-subitem">
                                <div class="nav-subitem-icon">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                                <span>Device Logs</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Reports & Analytics -->
                <!-- Reports & Analytics -->
                <div class="nav-group">
                    <button type="button" class="nav-toggle <?= in_array(($page ?? ''), ['reports']) ? 'active expanded' : '' ?>" data-target="reports-submenu">
                        <div class="flex items-center flex-1">
                            <div class="nav-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <span class="nav-text">Reports</span>
                        </div>
                        <div class="nav-arrow">
                            <i class="fas fa-chevron-down transform transition-transform duration-200 <?= in_array(($page ?? ''), ['reports']) ? 'rotate-180' : '' ?>"></i>
                        </div>
                    </button>
                    <div id="reports-submenu" class="nav-submenu <?= in_array(($page ?? ''), ['reports']) ? 'expanded' : '' ?>">
                        <div class="nav-submenu-content">
                            <a href="/dashboard/reports" class="nav-subitem <?= ($page ?? '') === 'reports' ? 'active' : '' ?>">
                                <div class="nav-subitem-icon">
                                    <i class="fas fa-analytics"></i>
                                </div>
                                <span>Analytics</span>
                            </a>
                            <a href="/dashboard/reports/attendance" class="nav-subitem">
                                <div class="nav-subitem-icon">
                                    <i class="fas fa-calendar-check"></i>
                                </div>
                                <span>Attendance Report</span>
                            </a>
                            <a href="/dashboard/reports/department" class="nav-subitem">
                                <div class="nav-subitem-icon">
                                    <i class="fas fa-building"></i>
                                </div>
                                <span>Department Report</span>
                            </a>
                            <a href="/dashboard/reports/overtime" class="nav-subitem">
                                <div class="nav-subitem-icon">
                                    <i class="fas fa-business-time"></i>
                                </div>
                                <span>Overtime Report</span>
                            </a>
                            <a href="/dashboard/reports/custom" class="nav-subitem">
                                <div class="nav-subitem-icon">
                                    <i class="fas fa-filter"></i>
                                </div>
                                <span>Custom Reports</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- API & Integration -->
                <!-- API & Integration -->
                <div class="nav-group">
                    <button type="button" class="nav-toggle" data-target="api-submenu">
                        <div class="flex items-center flex-1">
                            <div class="nav-icon">
                                <i class="fas fa-code"></i>
                            </div>
                            <span class="nav-text">API & Integration</span>
                        </div>
                        <div class="nav-arrow">
                            <i class="fas fa-chevron-down transform transition-transform duration-200"></i>
                        </div>
                    </button>
                    <div id="api-submenu" class="nav-submenu">
                        <div class="nav-submenu-content">
                            <a href="/examples/fingerspot/basic" class="nav-subitem">
                                <div class="nav-subitem-icon">
                                    <i class="fas fa-play"></i>
                                </div>
                                <span>API Examples</span>
                            </a>
                            <a href="/test/fingerspot" class="nav-subitem">
                                <div class="nav-subitem-icon">
                                    <i class="fas fa-vial"></i>
                                </div>
                                <span>API Testing</span>
                            </a>
                            <a href="/curl/fingerspot" class="nav-subitem">
                                <div class="nav-subitem-icon">
                                    <i class="fas fa-terminal"></i>
                                </div>
                                <span>cURL Tests</span>
                            </a>
                            <a href="/pdf/fingerspot" class="nav-subitem">
                                <div class="nav-subitem-icon">
                                    <i class="fas fa-file-pdf"></i>
                                </div>
                                <span>PDF Examples</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Settings -->
                <!-- Settings -->
                <div class="nav-group">
                    <button type="button" class="nav-toggle <?= in_array(($page ?? ''), ['settings']) ? 'active expanded' : '' ?>" data-target="settings-submenu">
                        <div class="flex items-center flex-1">
                            <div class="nav-icon">
                                <i class="fas fa-cog"></i>
                            </div>
                            <span class="nav-text">Settings</span>
                        </div>
                        <div class="nav-arrow">
                            <i class="fas fa-chevron-down transform transition-transform duration-200 <?= in_array(($page ?? ''), ['settings']) ? 'rotate-180' : '' ?>"></i>
                        </div>
                    </button>
                    <div id="settings-submenu" class="nav-submenu <?= in_array(($page ?? ''), ['settings']) ? 'expanded' : '' ?>">
                        <div class="nav-submenu-content">
                            <a href="/dashboard/settings" class="nav-subitem <?= ($page ?? '') === 'settings' ? 'active' : '' ?>">
                                <div class="nav-subitem-icon">
                                    <i class="fas fa-sliders-h"></i>
                                </div>
                                <span>General Settings</span>
                            </a>
                            <a href="/dashboard/settings/security" class="nav-subitem">
                                <div class="nav-subitem-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <span>Security</span>
                            </a>
                            <a href="/dashboard/settings/notifications" class="nav-subitem">
                                <div class="nav-subitem-icon">
                                    <i class="fas fa-bell"></i>
                                </div>
                                <span>Notifications</span>
                            </a>
                            <a href="/dashboard/settings/backup" class="nav-subitem">
                                <div class="nav-subitem-icon">
                                    <i class="fas fa-database"></i>
                                </div>
                                <span>Backup & Restore</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
        
        <div class="absolute bottom-4 left-4 right-4">
            <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-3">
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-300">Device Status</span>
                    <span id="device-status-indicator" class="flex items-center">
                        <i class="fas fa-circle text-xs status-offline mr-1"></i>
                        <span class="text-xs">Checking...</span>
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="ml-64 min-h-screen">
        <!-- Top Bar -->
        <header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between px-6 py-4">
                <div class="flex items-center">
                    <button id="sidebar-toggle" class="lg:hidden text-gray-500 hover:text-gray-700 mr-4">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-white"><?= $title ?? 'Dashboard' ?></h2>
                </div>
                
                <div class="flex items-center space-x-4">
                    <!-- Theme Toggle -->
                    <button id="theme-toggle" class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                        <i class="fas fa-moon dark:hidden"></i>
                        <i class="fas fa-sun hidden dark:inline"></i>
                    </button>
                    
                    <!-- Refresh Button -->
                    <button id="refresh-data" class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    
                    <!-- Notifications -->
                    <div class="relative">
                        <button class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                            <i class="fas fa-bell"></i>
                            <span id="notification-badge" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center hidden">0</span>
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Page Content -->
        <main class="p-6">
            <?= $this->renderSection('content') ?>
        </main>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 flex items-center space-x-3">
            <i class="fas fa-spinner loading-spinner text-blue-600"></i>
            <span class="text-gray-700 dark:text-gray-300">Loading...</span>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Global dashboard utilities
        const Dashboard = {
            // Show loading overlay
            showLoading() {
                document.getElementById('loading-overlay').classList.remove('hidden');
            },
            
            // Hide loading overlay
            hideLoading() {
                document.getElementById('loading-overlay').classList.add('hidden');
            },
            
            // Show toast notification
            showToast(message, type = 'info') {
                const toast = document.createElement('div');
                const bgColor = {
                    'success': 'bg-green-500',
                    'error': 'bg-red-500',
                    'warning': 'bg-yellow-500',
                    'info': 'bg-blue-500'
                }[type] || 'bg-blue-500';
                
                toast.className = `${bgColor} text-white px-6 py-3 rounded-lg shadow-lg fade-in`;
                toast.innerHTML = `
                    <div class="flex items-center justify-between">
                        <span>${message}</span>
                        <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;
                
                document.getElementById('toast-container').appendChild(toast);
                
                // Auto remove after 5 seconds
                setTimeout(() => {
                    if (toast.parentElement) {
                        toast.remove();
                    }
                }, 5000);
            },
            
            // Update device status indicator
            updateDeviceStatus(status) {
                const indicator = document.getElementById('device-status-indicator');
                const icon = indicator.querySelector('i');
                const text = indicator.querySelector('span');
                
                icon.className = 'fas fa-circle text-xs mr-1 ' + 
                    (status === 'online' ? 'status-online' : 
                     status === 'offline' ? 'status-offline' : 'status-warning');
                text.textContent = status.charAt(0).toUpperCase() + status.slice(1);
            }
        };

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            // Theme toggle
            const themeToggle = document.getElementById('theme-toggle');
            const html = document.documentElement;

            themeToggle.addEventListener('click', function() {
                html.classList.toggle('dark');
                localStorage.setItem('theme', html.classList.contains('dark') ? 'dark' : 'light');
            });

            // Load saved theme
            if (localStorage.getItem('theme') === 'dark') {
                html.classList.add('dark');
            }

            // Initialize multi-level navigation
            initializeNavigation();

            // Show welcome message
            setTimeout(() => {
                Dashboard.showToast('Welcome to Fingerspot Dashboard! Click on menu items to expand them.', 'info');
            }, 1000);

            // Initialize sidebar collapse
            initializeSidebarCollapse();

            // Sidebar toggle for mobile
            const sidebarToggle = document.getElementById('sidebar-toggle');
            const sidebarToggleMobile = document.getElementById('sidebar-toggle-mobile');
            const sidebar = document.getElementById('sidebar');

            sidebarToggle?.addEventListener('click', function() {
                sidebar.classList.toggle('show');
            });

            sidebarToggleMobile?.addEventListener('click', function() {
                sidebar.classList.remove('show');
            });

            // Close sidebar when clicking outside on mobile
            document.addEventListener('click', function(e) {
                if (window.innerWidth <= 1024) {
                    if (!sidebar.contains(e.target) && !sidebarToggle?.contains(e.target)) {
                        sidebar.classList.remove('show');
                    }
                }
            });

            // Refresh data button
            document.getElementById('refresh-data').addEventListener('click', function() {
                if (typeof refreshDashboardData === 'function') {
                    refreshDashboardData();
                } else {
                    location.reload();
                }
            });

            // Load initial device status
            fetch('/dashboard/api/device-status')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Dashboard.updateDeviceStatus(data.data.status);
                    }
                })
                .catch(() => {
                    Dashboard.updateDeviceStatus('offline');
                });
        });

        // Enhanced Multi-level Navigation System
        function initializeNavigation() {
            // Handle menu toggle clicks
            const navToggles = document.querySelectorAll('.nav-toggle');

            navToggles.forEach((toggle) => {
                toggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    const targetId = this.getAttribute('data-target');
                    const submenu = document.getElementById(targetId);
                    const arrow = this.querySelector('.nav-arrow i');

                    if (submenu) {
                        // Toggle current submenu
                        const isExpanded = submenu.classList.contains('expanded');

                        if (isExpanded) {
                            // Close submenu
                            submenu.classList.remove('expanded');
                            this.classList.remove('expanded');
                            if (arrow) {
                                arrow.classList.remove('rotate-180');
                            }
                        } else {
                            // Close other open submenus (accordion behavior)
                            closeAllSubmenus();

                            // Open current submenu
                            submenu.classList.add('expanded');
                            this.classList.add('expanded');
                            if (arrow) {
                                arrow.classList.add('rotate-180');
                            }
                        }

                        // Save menu state
                        saveMenuState();

                        // Show visual feedback
                        const menuName = this.querySelector('.nav-text').textContent;
                        Dashboard.showToast(`${isExpanded ? 'Collapsed' : 'Expanded'} ${menuName} menu`, 'info');
                    }
                });
            });

            // Handle submenu item clicks
            const subItems = document.querySelectorAll('.nav-subitem');
            subItems.forEach(item => {
                item.addEventListener('click', function() {
                    // Remove active class from all subitems
                    subItems.forEach(si => si.classList.remove('active'));
                    // Add active class to clicked item
                    this.classList.add('active');

                    // Close sidebar on mobile after navigation
                    if (window.innerWidth <= 1024) {
                        document.getElementById('sidebar').classList.remove('show');
                    }
                });
            });

            // Restore menu state from localStorage
            restoreMenuState();
        }

        function closeAllSubmenus() {
            const submenus = document.querySelectorAll('.nav-submenu');
            const arrows = document.querySelectorAll('.nav-arrow');
            const toggles = document.querySelectorAll('.nav-toggle');

            submenus.forEach(submenu => submenu.classList.remove('expanded'));
            arrows.forEach(arrow => {
                const icon = arrow.querySelector('i');
                if (icon) icon.classList.remove('rotate-180');
            });
            toggles.forEach(toggle => toggle.classList.remove('expanded'));
        }

        function saveMenuState() {
            const openMenus = [];
            const submenus = document.querySelectorAll('.nav-submenu.expanded');

            submenus.forEach(submenu => {
                openMenus.push(submenu.id);
            });

            localStorage.setItem('openMenus', JSON.stringify(openMenus));
        }

        function restoreMenuState() {
            const openMenus = JSON.parse(localStorage.getItem('openMenus') || '[]');

            openMenus.forEach(menuId => {
                const submenu = document.getElementById(menuId);
                if (submenu) {
                    const toggle = document.querySelector(`[data-target="${menuId}"]`);
                    const arrow = toggle?.querySelector('.nav-arrow');

                    submenu.classList.add('expanded');
                    toggle?.classList.add('expanded');
                    const arrowIcon = arrow?.querySelector('i');
                    if (arrowIcon) arrowIcon.classList.add('rotate-180');
                }
            });
        }

        // Handle window resize
        window.addEventListener('resize', function() {
            const sidebar = document.getElementById('sidebar');

            if (window.innerWidth > 1024) {
                // Desktop: ensure sidebar is visible
                sidebar.classList.remove('show');
            }
        });

        // Sidebar Collapse Functionality
        function initializeSidebarCollapse() {
            const collapseBtn = document.getElementById('sidebar-collapse');
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.querySelector('.ml-64');

            collapseBtn?.addEventListener('click', function() {
                const isCollapsed = sidebar.classList.contains('collapsed');

                if (isCollapsed) {
                    // Expand sidebar
                    sidebar.classList.remove('collapsed');
                    mainContent?.classList.remove('ml-16');
                    mainContent?.classList.add('ml-64');
                    this.innerHTML = '<i class="fas fa-chevron-left"></i>';
                    localStorage.setItem('sidebarCollapsed', 'false');
                } else {
                    // Collapse sidebar
                    sidebar.classList.add('collapsed');
                    mainContent?.classList.remove('ml-64');
                    mainContent?.classList.add('ml-16');
                    this.innerHTML = '<i class="fas fa-chevron-right"></i>';
                    localStorage.setItem('sidebarCollapsed', 'true');

                    // Close all submenus when collapsing
                    closeAllSubmenus();
                }
            });

            // Restore collapse state
            if (localStorage.getItem('sidebarCollapsed') === 'true') {
                sidebar.classList.add('collapsed');
                mainContent?.classList.remove('ml-64');
                mainContent?.classList.add('ml-16');
                collapseBtn.innerHTML = '<i class="fas fa-chevron-right"></i>';
            }
        }

        // Update notification badges
        function updateNotificationBadges() {
            // Simulate real-time updates
            const realtimeBadge = document.getElementById('realtime-badge');
            const apiLogsBadge = document.getElementById('api-logs-badge');

            // Update real-time attendance badge
            fetch('/dashboard/api/recent-logs')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.data.data) {
                        const count = data.data.data.length;
                        if (count > 0) {
                            realtimeBadge.textContent = count;
                            realtimeBadge.classList.remove('hidden');
                        } else {
                            realtimeBadge.classList.add('hidden');
                        }
                    }
                })
                .catch(() => {
                    realtimeBadge.classList.add('hidden');
                });

            // Update API logs badge (simulate)
            const apiLogCount = Math.floor(Math.random() * 5);
            if (apiLogCount > 0) {
                apiLogsBadge.textContent = apiLogCount;
                apiLogsBadge.classList.remove('hidden');
            } else {
                apiLogsBadge.classList.add('hidden');
            }
        }

        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            // ESC key closes sidebar on mobile
            if (e.key === 'Escape' && window.innerWidth <= 1024) {
                document.getElementById('sidebar').classList.remove('show');
            }

            // Ctrl+B toggles sidebar collapse on desktop
            if (e.ctrlKey && e.key === 'b' && window.innerWidth > 1024) {
                e.preventDefault();
                document.getElementById('sidebar-collapse')?.click();
            }
        });

        // Update badges periodically
        setInterval(updateNotificationBadges, 30000); // Every 30 seconds
        updateNotificationBadges(); // Initial load
    </script>
    
    <style>
        /* Modern Navigation Styles */
        .nav-item {
            @apply flex items-center w-full px-4 py-3 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200;
            text-decoration: none;
        }

        .nav-item.active {
            @apply bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg;
        }

        .nav-item:hover {
            @apply transform translate-x-1;
        }

        .nav-icon {
            @apply w-10 h-10 flex items-center justify-center rounded-lg mr-3 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400;
            transition: all 0.2s ease;
        }

        .nav-item.active .nav-icon {
            @apply bg-white bg-opacity-20 text-white;
        }

        .nav-text {
            @apply font-medium;
        }

        /* Navigation Groups */
        .nav-group {
            @apply mb-1;
        }

        /* Navigation Toggle Buttons */
        .nav-toggle {
            @apply flex items-center w-full px-4 py-3 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200 border-none bg-transparent cursor-pointer;
        }

        .nav-toggle:focus {
            @apply outline-none ring-2 ring-blue-500 ring-opacity-50;
        }

        .nav-toggle.active,
        .nav-toggle.expanded {
            @apply bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300;
        }

        .nav-toggle:hover {
            @apply transform translate-x-1;
        }

        .nav-arrow {
            @apply ml-auto text-gray-400 transition-all duration-200;
        }

        .nav-toggle:hover .nav-arrow {
            @apply text-gray-600 dark:text-gray-300;
        }

        /* Submenu Styles */
        .nav-submenu {
            @apply overflow-hidden transition-all duration-300 ease-in-out;
            max-height: 0;
            opacity: 0;
        }

        .nav-submenu.expanded {
            max-height: 500px;
            opacity: 1;
        }

        .nav-submenu-content {
            @apply mt-2 ml-4 pl-4 border-l-2 border-gray-200 dark:border-gray-700 space-y-1;
        }

        .nav-subitem {
            @apply flex items-center px-3 py-2 text-sm text-gray-600 dark:text-gray-400 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200;
            text-decoration: none;
        }

        .nav-subitem.active {
            @apply bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300;
        }

        .nav-subitem:hover {
            @apply transform translate-x-1 text-gray-800 dark:text-gray-200;
        }

        .nav-subitem-icon {
            @apply w-8 h-8 flex items-center justify-center rounded-lg mr-3 bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400;
            transition: all 0.2s ease;
        }

        .nav-subitem.active .nav-subitem-icon {
            @apply bg-blue-100 dark:bg-blue-800 text-blue-600 dark:text-blue-400;
        }

        .nav-subitem:hover .nav-subitem-icon {
            @apply bg-gray-200 dark:bg-gray-600;
        }

        /* Navigation Badges */
        .nav-badge {
            @apply ml-auto px-2 py-1 text-xs font-medium text-white rounded-full;
        }

        /* Mobile Responsive */
        @media (max-width: 1024px) {
            #sidebar {
                @apply transform -translate-x-full;
            }

            #sidebar.show {
                @apply transform translate-x-0;
            }

            .nav-submenu.show {
                max-height: 300px;
            }
        }

        /* Enhanced Visual Effects */
        .nav-item:hover .nav-icon,
        .nav-toggle:hover .nav-icon {
            @apply transform scale-110;
        }

        .nav-subitem:hover .nav-subitem-icon {
            @apply transform scale-110;
        }

        /* Sidebar Animations */
        .sidebar-transition {
            transition: transform 0.3s ease-in-out;
        }

        /* Active Menu Indicator */
        .nav-toggle.expanded::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(to bottom, #3b82f6, #8b5cf6);
            border-radius: 0 2px 2px 0;
        }

        /* Smooth Hover Effects */
        .nav-item,
        .nav-toggle,
        .nav-subitem {
            position: relative;
            overflow: hidden;
        }

        .nav-item::before,
        .nav-toggle::before,
        .nav-subitem::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s;
        }

        .nav-item:hover::before,
        .nav-toggle:hover::before,
        .nav-subitem:hover::before {
            left: 100%;
        }

        /* Sidebar Animation */
        .sidebar-transition {
            transition: transform 0.3s ease-in-out;
        }

        /* Menu Badge */
        .nav-badge {
            @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300 ml-auto;
        }

        /* Tooltip for collapsed sidebar */
        .nav-tooltip {
            @apply absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 pointer-events-none transition-opacity duration-200;
            top: 50%;
            transform: translateY(-50%);
        }

        .nav-item:hover .nav-tooltip {
            @apply opacity-100;
        }
    </style>
</body>
</html>
