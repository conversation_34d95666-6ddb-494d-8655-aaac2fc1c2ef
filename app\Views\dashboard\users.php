<?= $this->extend('dashboard/layout') ?>

<?= $this->section('content') ?>

<!-- <PERSON> Header -->
<div class="mb-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">User Management</h1>
            <p class="text-gray-600 dark:text-gray-400">Manage employee profiles and biometric data</p>
        </div>
        <div class="flex items-center space-x-3">
            <button id="bulk-import" class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                <i class="fas fa-upload mr-2"></i>
                Bulk Import
            </button>
            <button id="add-user" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-user-plus mr-2"></i>
                Add User
            </button>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 mb-6">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Search Users</label>
            <input type="text" id="user-search" placeholder="Name, PIN, or Employee ID" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
        </div>
        
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Department</label>
            <select id="department-filter" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                <option value="">All Departments</option>
                <option value="IT">IT</option>
                <option value="HR">HR</option>
                <option value="Finance">Finance</option>
                <option value="Operations">Operations</option>
            </select>
        </div>
        
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</label>
            <select id="status-filter" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                <option value="">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="pending">Pending</option>
            </select>
        </div>
        
        <div class="flex items-end">
            <button id="apply-filters" class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-search mr-2"></i>
                Search
            </button>
        </div>
    </div>
</div>

<!-- User Statistics -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Users</p>
                <p id="total-users" class="text-2xl font-bold text-gray-900 dark:text-white">--</p>
            </div>
            <div class="p-3 bg-blue-100 dark:bg-blue-900 rounded-full">
                <i class="fas fa-users text-blue-600 dark:text-blue-400"></i>
            </div>
        </div>
    </div>
    
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Users</p>
                <p id="active-users" class="text-2xl font-bold text-green-600 dark:text-green-400">--</p>
            </div>
            <div class="p-3 bg-green-100 dark:bg-green-900 rounded-full">
                <i class="fas fa-user-check text-green-600 dark:text-green-400"></i>
            </div>
        </div>
    </div>
    
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">With Biometrics</p>
                <p id="biometric-users" class="text-2xl font-bold text-purple-600 dark:text-purple-400">--</p>
            </div>
            <div class="p-3 bg-purple-100 dark:bg-purple-900 rounded-full">
                <i class="fas fa-fingerprint text-purple-600 dark:text-purple-400"></i>
            </div>
        </div>
    </div>
    
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">New This Month</p>
                <p id="new-users" class="text-2xl font-bold text-orange-600 dark:text-orange-400">--</p>
            </div>
            <div class="p-3 bg-orange-100 dark:bg-orange-900 rounded-full">
                <i class="fas fa-user-plus text-orange-600 dark:text-orange-400"></i>
            </div>
        </div>
    </div>
</div>

<!-- Users Table -->
<div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm">
    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">User Directory</h3>
            <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-500 dark:text-gray-400">
                    Showing <span id="showing-count">0</span> of <span id="total-count">0</span> users
                </span>
                <button id="refresh-table" class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-sync-alt"></i>
                </button>
            </div>
        </div>
    </div>
    
    <div class="overflow-x-auto">
        <table class="w-full">
            <thead class="bg-gray-50 dark:bg-gray-700">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        <input type="checkbox" id="select-all" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Employee
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Department
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Status
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Biometric
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Last Activity
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Actions
                    </th>
                </tr>
            </thead>
            <tbody id="users-table-body" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                <!-- Loading state -->
                <tr>
                    <td colspan="7" class="px-6 py-8 text-center">
                        <div class="flex items-center justify-center">
                            <i class="fas fa-spinner loading-spinner text-gray-400 mr-2"></i>
                            <span class="text-gray-500 dark:text-gray-400">Loading users...</span>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <!-- Pagination -->
    <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-700 dark:text-gray-300">Rows per page:</span>
                <select id="rows-per-page" class="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700 dark:text-white">
                    <option value="10">10</option>
                    <option value="25" selected>25</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                </select>
            </div>
            
            <div class="flex items-center space-x-2">
                <button id="prev-page" class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50" disabled>
                    <i class="fas fa-chevron-left"></i>
                </button>
                <span id="page-info" class="text-sm text-gray-700 dark:text-gray-300">Page 1 of 1</span>
                <button id="next-page" class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50" disabled>
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit User Modal -->
<div id="user-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-md w-full mx-4">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <h3 id="modal-title" class="text-lg font-semibold text-gray-900 dark:text-white">Add New User</h3>
                <button id="close-modal" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        
        <form id="user-form" class="p-6 space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">PIN</label>
                <input type="text" id="user-pin" required class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Full Name</label>
                <input type="text" id="user-name" required class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Employee ID</label>
                <input type="text" id="user-employee-id" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Department</label>
                <select id="user-department" required class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                    <option value="">Select Department</option>
                    <option value="IT">IT</option>
                    <option value="HR">HR</option>
                    <option value="Finance">Finance</option>
                    <option value="Operations">Operations</option>
                </select>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Position</label>
                <input type="text" id="user-position" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</label>
                <select id="user-status" required class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                    <option value="pending">Pending</option>
                </select>
            </div>
            
            <div class="flex items-center justify-end space-x-3 pt-4">
                <button type="button" id="cancel-user" class="px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700">
                    Cancel
                </button>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                    Save User
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// User management JavaScript
let currentPage = 1;
let rowsPerPage = 25;
let totalRecords = 0;
let currentFilters = {};
let editingUserId = null;

// Initialize users page
function initUsersPage() {
    loadUsersData();
    loadUserStats();
}

// Load users data
function loadUsersData() {
    const tableBody = document.getElementById('users-table-body');
    tableBody.innerHTML = `
        <tr>
            <td colspan="7" class="px-6 py-8 text-center">
                <div class="flex items-center justify-center">
                    <i class="fas fa-spinner loading-spinner text-gray-400 mr-2"></i>
                    <span class="text-gray-500 dark:text-gray-400">Loading users...</span>
                </div>
            </td>
        </tr>
    `;
    
    // Simulate API call
    setTimeout(() => {
        const sampleData = generateSampleUsersData();
        displayUsersData(sampleData);
        updatePagination();
    }, 1000);
}

// Load user statistics
function loadUserStats() {
    setTimeout(() => {
        document.getElementById('total-users').textContent = '156';
        document.getElementById('active-users').textContent = '142';
        document.getElementById('biometric-users').textContent = '128';
        document.getElementById('new-users').textContent = '8';
    }, 500);
}

// Generate sample users data
function generateSampleUsersData() {
    const sampleData = [];
    const names = ['John Doe', 'Jane Smith', 'Mike Johnson', 'Sarah Wilson', 'David Brown', 'Lisa Garcia', 'Tom Anderson', 'Emily Davis'];
    const departments = ['IT', 'HR', 'Finance', 'Operations'];
    const positions = ['Manager', 'Developer', 'Analyst', 'Coordinator', 'Specialist'];
    const statuses = ['active', 'inactive', 'pending'];
    
    for (let i = 0; i < 50; i++) {
        const lastActivity = new Date();
        lastActivity.setDate(lastActivity.getDate() - Math.floor(Math.random() * 30));
        
        sampleData.push({
            id: i + 1,
            pin: String(1000 + i).padStart(4, '0'),
            name: names[Math.floor(Math.random() * names.length)],
            employeeId: `EMP${String(1000 + i).padStart(4, '0')}`,
            department: departments[Math.floor(Math.random() * departments.length)],
            position: positions[Math.floor(Math.random() * positions.length)],
            status: statuses[Math.floor(Math.random() * statuses.length)],
            hasBiometric: Math.random() > 0.3,
            lastActivity: lastActivity.toISOString().split('T')[0],
            createdAt: new Date(2024, 0, 1 + Math.floor(Math.random() * 365)).toISOString().split('T')[0]
        });
    }
    
    return sampleData;
}

// Display users data in table
function displayUsersData(data) {
    const tableBody = document.getElementById('users-table-body');
    
    if (data.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="7" class="px-6 py-8 text-center text-gray-500 dark:text-gray-400">
                    No users found
                </td>
            </tr>
        `;
        return;
    }
    
    const startIndex = (currentPage - 1) * rowsPerPage;
    const endIndex = startIndex + rowsPerPage;
    const pageData = data.slice(startIndex, endIndex);
    
    const rows = pageData.map(user => `
        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
            <td class="px-6 py-4 whitespace-nowrap">
                <input type="checkbox" class="user-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500" value="${user.id}">
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mr-3">
                        <span class="text-white font-medium text-sm">${user.name.split(' ').map(n => n[0]).join('')}</span>
                    </div>
                    <div>
                        <div class="text-sm font-medium text-gray-900 dark:text-white">${user.name}</div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">PIN: ${user.pin} | ID: ${user.employeeId}</div>
                    </div>
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900 dark:text-white">${user.department}</div>
                <div class="text-sm text-gray-500 dark:text-gray-400">${user.position}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    user.status === 'active' 
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                        : user.status === 'inactive'
                        ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                        : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
                }">
                    ${user.status.charAt(0).toUpperCase() + user.status.slice(1)}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center ${user.hasBiometric ? 'text-green-600 dark:text-green-400' : 'text-gray-400'}">
                    <i class="fas fa-fingerprint mr-1"></i>
                    ${user.hasBiometric ? 'Enrolled' : 'Not Enrolled'}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                ${new Date(user.lastActivity).toLocaleDateString()}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <button onclick="editUser(${user.id})" class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3" title="Edit">
                    <i class="fas fa-edit"></i>
                </button>
                <button onclick="enrollBiometric(${user.id})" class="text-purple-600 hover:text-purple-900 dark:text-purple-400 dark:hover:text-purple-300 mr-3" title="Biometric">
                    <i class="fas fa-fingerprint"></i>
                </button>
                <button onclick="deleteUser(${user.id})" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300" title="Delete">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');
    
    tableBody.innerHTML = rows;
    
    // Update counters
    document.getElementById('showing-count').textContent = pageData.length;
    document.getElementById('total-count').textContent = data.length;
    totalRecords = data.length;
}

// Update pagination controls
function updatePagination() {
    const totalPages = Math.ceil(totalRecords / rowsPerPage);
    
    document.getElementById('page-info').textContent = `Page ${currentPage} of ${totalPages}`;
    document.getElementById('prev-page').disabled = currentPage === 1;
    document.getElementById('next-page').disabled = currentPage === totalPages;
}

// Show add/edit user modal
function showUserModal(isEdit = false, userData = null) {
    const modal = document.getElementById('user-modal');
    const title = document.getElementById('modal-title');
    const form = document.getElementById('user-form');
    
    title.textContent = isEdit ? 'Edit User' : 'Add New User';
    editingUserId = isEdit ? userData.id : null;
    
    if (isEdit && userData) {
        document.getElementById('user-pin').value = userData.pin;
        document.getElementById('user-name').value = userData.name;
        document.getElementById('user-employee-id').value = userData.employeeId;
        document.getElementById('user-department').value = userData.department;
        document.getElementById('user-position').value = userData.position;
        document.getElementById('user-status').value = userData.status;
    } else {
        form.reset();
    }
    
    modal.classList.remove('hidden');
}

// Hide user modal
function hideUserModal() {
    document.getElementById('user-modal').classList.add('hidden');
    editingUserId = null;
}

// Save user
function saveUser(formData) {
    Dashboard.showLoading();
    
    // Simulate API call
    setTimeout(() => {
        Dashboard.hideLoading();
        hideUserModal();
        Dashboard.showToast(editingUserId ? 'User updated successfully' : 'User created successfully', 'success');
        loadUsersData();
        loadUserStats();
    }, 1500);
}

// Edit user
function editUser(userId) {
    // In a real app, fetch user data from API
    const userData = {
        id: userId,
        pin: '1001',
        name: 'John Doe',
        employeeId: 'EMP1001',
        department: 'IT',
        position: 'Developer',
        status: 'active'
    };
    
    showUserModal(true, userData);
}

// Delete user
function deleteUser(userId) {
    if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
        Dashboard.showLoading();
        
        setTimeout(() => {
            Dashboard.hideLoading();
            Dashboard.showToast('User deleted successfully', 'success');
            loadUsersData();
            loadUserStats();
        }, 1000);
    }
}

// Enroll biometric
function enrollBiometric(userId) {
    Dashboard.showToast('Initiating biometric enrollment...', 'info');
    
    setTimeout(() => {
        Dashboard.showToast('Biometric enrollment completed successfully', 'success');
        loadUsersData();
    }, 3000);
}

// Apply filters
function applyFilters() {
    currentFilters = {
        search: document.getElementById('user-search').value,
        department: document.getElementById('department-filter').value,
        status: document.getElementById('status-filter').value
    };
    
    currentPage = 1;
    loadUsersData();
    Dashboard.showToast('Filters applied successfully', 'success');
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    initUsersPage();
    
    // Modal controls
    document.getElementById('add-user').addEventListener('click', () => showUserModal());
    document.getElementById('close-modal').addEventListener('click', hideUserModal);
    document.getElementById('cancel-user').addEventListener('click', hideUserModal);
    
    // Form submission
    document.getElementById('user-form').addEventListener('submit', function(e) {
        e.preventDefault();
        const formData = new FormData(this);
        saveUser(formData);
    });
    
    // Filter and action buttons
    document.getElementById('apply-filters').addEventListener('click', applyFilters);
    document.getElementById('refresh-table').addEventListener('click', loadUsersData);
    
    // Pagination
    document.getElementById('prev-page').addEventListener('click', () => {
        if (currentPage > 1) {
            currentPage--;
            loadUsersData();
        }
    });
    
    document.getElementById('next-page').addEventListener('click', () => {
        const totalPages = Math.ceil(totalRecords / rowsPerPage);
        if (currentPage < totalPages) {
            currentPage++;
            loadUsersData();
        }
    });
    
    document.getElementById('rows-per-page').addEventListener('change', (e) => {
        rowsPerPage = parseInt(e.target.value);
        currentPage = 1;
        loadUsersData();
    });
    
    // Select all checkbox
    document.getElementById('select-all').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.user-checkbox');
        checkboxes.forEach(cb => cb.checked = this.checked);
    });
});
</script>

<?= $this->endSection() ?>
