<?= $this->extend('dashboard/layout') ?>

<?= $this->section('content') ?>

<!-- <PERSON> Header -->
<div class="mb-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Reports & Analytics</h1>
            <p class="text-gray-600 dark:text-gray-400">Comprehensive attendance and performance analytics</p>
        </div>
        <div class="flex items-center space-x-3">
            <button id="export-report" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                <i class="fas fa-download mr-2"></i>
                Export Report
            </button>
            <button id="schedule-report" class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                <i class="fas fa-calendar mr-2"></i>
                Schedule Report
            </button>
        </div>
    </div>
</div>

<!-- Report Filters -->
<div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 mb-6">
    <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Report Type</label>
            <select id="report-type" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                <option value="attendance">Attendance Summary</option>
                <option value="daily">Daily Report</option>
                <option value="monthly">Monthly Report</option>
                <option value="department">Department Report</option>
                <option value="overtime">Overtime Report</option>
            </select>
        </div>
        
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Start Date</label>
            <input type="date" id="start-date" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
        </div>
        
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">End Date</label>
            <input type="date" id="end-date" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
        </div>
        
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Department</label>
            <select id="department-filter" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                <option value="">All Departments</option>
                <option value="IT">IT</option>
                <option value="HR">HR</option>
                <option value="Finance">Finance</option>
                <option value="Operations">Operations</option>
            </select>
        </div>
        
        <div class="flex items-end">
            <button id="generate-report" class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-chart-bar mr-2"></i>
                Generate Report
            </button>
        </div>
    </div>
</div>

<!-- Key Metrics -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Average Attendance</p>
                <p id="avg-attendance" class="text-2xl font-bold text-green-600 dark:text-green-400">--</p>
            </div>
            <div class="p-3 bg-green-100 dark:bg-green-900 rounded-full">
                <i class="fas fa-percentage text-green-600 dark:text-green-400"></i>
            </div>
        </div>
    </div>
    
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Late Arrivals</p>
                <p id="late-arrivals" class="text-2xl font-bold text-orange-600 dark:text-orange-400">--</p>
            </div>
            <div class="p-3 bg-orange-100 dark:bg-orange-900 rounded-full">
                <i class="fas fa-clock text-orange-600 dark:text-orange-400"></i>
            </div>
        </div>
    </div>
    
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Overtime Hours</p>
                <p id="overtime-hours" class="text-2xl font-bold text-purple-600 dark:text-purple-400">--</p>
            </div>
            <div class="p-3 bg-purple-100 dark:bg-purple-900 rounded-full">
                <i class="fas fa-business-time text-purple-600 dark:text-purple-400"></i>
            </div>
        </div>
    </div>
    
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Absent Days</p>
                <p id="absent-days" class="text-2xl font-bold text-red-600 dark:text-red-400">--</p>
            </div>
            <div class="p-3 bg-red-100 dark:bg-red-900 rounded-full">
                <i class="fas fa-user-times text-red-600 dark:text-red-400"></i>
            </div>
        </div>
    </div>
</div>

<!-- Charts Section -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Attendance Trend Chart -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Attendance Trend</h3>
            <div class="flex items-center space-x-2">
                <button class="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">7D</button>
                <button class="text-sm bg-blue-100 text-blue-600 px-2 py-1 rounded dark:bg-blue-900 dark:text-blue-300">30D</button>
                <button class="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">90D</button>
            </div>
        </div>
        <div class="h-64">
            <canvas id="attendance-chart"></canvas>
        </div>
    </div>

    <!-- Department Comparison -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Department Comparison</h3>
            <button class="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">View Details</button>
        </div>
        <div class="h-64">
            <canvas id="department-chart"></canvas>
        </div>
    </div>
</div>

<!-- Detailed Reports Table -->
<div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm">
    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Detailed Report</h3>
            <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-500 dark:text-gray-400">
                    Showing <span id="showing-count">0</span> of <span id="total-count">0</span> records
                </span>
                <button id="refresh-report" class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-sync-alt"></i>
                </button>
            </div>
        </div>
    </div>
    
    <div class="overflow-x-auto">
        <table class="w-full">
            <thead class="bg-gray-50 dark:bg-gray-700">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Employee
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Department
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Days Present
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Days Absent
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Late Arrivals
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Overtime Hours
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Attendance %
                    </th>
                </tr>
            </thead>
            <tbody id="report-table-body" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                <!-- Loading state -->
                <tr>
                    <td colspan="7" class="px-6 py-8 text-center">
                        <div class="flex items-center justify-center">
                            <i class="fas fa-spinner loading-spinner text-gray-400 mr-2"></i>
                            <span class="text-gray-500 dark:text-gray-400">Loading report data...</span>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <!-- Pagination -->
    <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-700 dark:text-gray-300">Rows per page:</span>
                <select id="rows-per-page" class="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700 dark:text-white">
                    <option value="10">10</option>
                    <option value="25" selected>25</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                </select>
            </div>
            
            <div class="flex items-center space-x-2">
                <button id="prev-page" class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50" disabled>
                    <i class="fas fa-chevron-left"></i>
                </button>
                <span id="page-info" class="text-sm text-gray-700 dark:text-gray-300">Page 1 of 1</span>
                <button id="next-page" class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50" disabled>
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Reports page JavaScript
let attendanceChart, departmentChart;
let currentPage = 1;
let rowsPerPage = 25;
let totalRecords = 0;

// Initialize reports page
function initReportsPage() {
    // Set default date range (last 30 days)
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30);
    
    document.getElementById('start-date').value = startDate.toISOString().split('T')[0];
    document.getElementById('end-date').value = endDate.toISOString().split('T')[0];
    
    loadReportMetrics();
    initializeCharts();
    loadReportData();
}

// Load report metrics
function loadReportMetrics() {
    setTimeout(() => {
        document.getElementById('avg-attendance').textContent = '94.2%';
        document.getElementById('late-arrivals').textContent = '23';
        document.getElementById('overtime-hours').textContent = '156h';
        document.getElementById('absent-days').textContent = '12';
    }, 500);
}

// Initialize charts
function initializeCharts() {
    // Attendance Trend Chart
    const attendanceCtx = document.getElementById('attendance-chart').getContext('2d');
    attendanceChart = new Chart(attendanceCtx, {
        type: 'line',
        data: {
            labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
            datasets: [{
                label: 'Attendance Rate',
                data: [92, 95, 88, 96],
                borderColor: 'rgb(59, 130, 246)',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                }
            }
        }
    });

    // Department Comparison Chart
    const departmentCtx = document.getElementById('department-chart').getContext('2d');
    departmentChart = new Chart(departmentCtx, {
        type: 'doughnut',
        data: {
            labels: ['IT', 'HR', 'Finance', 'Operations'],
            datasets: [{
                data: [35, 20, 25, 20],
                backgroundColor: [
                    'rgb(59, 130, 246)',
                    'rgb(16, 185, 129)',
                    'rgb(245, 158, 11)',
                    'rgb(139, 92, 246)'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// Load report data
function loadReportData() {
    const tableBody = document.getElementById('report-table-body');
    tableBody.innerHTML = `
        <tr>
            <td colspan="7" class="px-6 py-8 text-center">
                <div class="flex items-center justify-center">
                    <i class="fas fa-spinner loading-spinner text-gray-400 mr-2"></i>
                    <span class="text-gray-500 dark:text-gray-400">Loading report data...</span>
                </div>
            </td>
        </tr>
    `;
    
    // Simulate API call
    setTimeout(() => {
        const sampleData = generateSampleReportData();
        displayReportData(sampleData);
        updatePagination();
    }, 1000);
}

// Generate sample report data
function generateSampleReportData() {
    const sampleData = [];
    const names = ['John Doe', 'Jane Smith', 'Mike Johnson', 'Sarah Wilson', 'David Brown', 'Lisa Garcia', 'Tom Anderson', 'Emily Davis'];
    const departments = ['IT', 'HR', 'Finance', 'Operations'];
    
    for (let i = 0; i < 50; i++) {
        const daysPresent = Math.floor(Math.random() * 5) + 18; // 18-22 days
        const daysAbsent = 22 - daysPresent;
        const lateArrivals = Math.floor(Math.random() * 5);
        const overtimeHours = Math.floor(Math.random() * 20);
        const attendancePercent = Math.round((daysPresent / 22) * 100);
        
        sampleData.push({
            id: i + 1,
            name: names[Math.floor(Math.random() * names.length)],
            department: departments[Math.floor(Math.random() * departments.length)],
            daysPresent,
            daysAbsent,
            lateArrivals,
            overtimeHours,
            attendancePercent
        });
    }
    
    return sampleData;
}

// Display report data in table
function displayReportData(data) {
    const tableBody = document.getElementById('report-table-body');
    
    if (data.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="7" class="px-6 py-8 text-center text-gray-500 dark:text-gray-400">
                    No report data found
                </td>
            </tr>
        `;
        return;
    }
    
    const startIndex = (currentPage - 1) * rowsPerPage;
    const endIndex = startIndex + rowsPerPage;
    const pageData = data.slice(startIndex, endIndex);
    
    const rows = pageData.map(record => `
        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mr-3">
                        <span class="text-white font-medium text-xs">${record.name.split(' ').map(n => n[0]).join('')}</span>
                    </div>
                    <div class="text-sm font-medium text-gray-900 dark:text-white">${record.name}</div>
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                ${record.department}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                ${record.daysPresent}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                ${record.daysAbsent}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                ${record.lateArrivals}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                ${record.overtimeHours}h
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    record.attendancePercent >= 95 
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                        : record.attendancePercent >= 85
                        ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
                        : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                }">
                    ${record.attendancePercent}%
                </span>
            </td>
        </tr>
    `).join('');
    
    tableBody.innerHTML = rows;
    
    // Update counters
    document.getElementById('showing-count').textContent = pageData.length;
    document.getElementById('total-count').textContent = data.length;
    totalRecords = data.length;
}

// Update pagination controls
function updatePagination() {
    const totalPages = Math.ceil(totalRecords / rowsPerPage);
    
    document.getElementById('page-info').textContent = `Page ${currentPage} of ${totalPages}`;
    document.getElementById('prev-page').disabled = currentPage === 1;
    document.getElementById('next-page').disabled = currentPage === totalPages;
}

// Generate report
function generateReport() {
    Dashboard.showLoading();
    Dashboard.showToast('Generating report...', 'info');
    
    setTimeout(() => {
        Dashboard.hideLoading();
        Dashboard.showToast('Report generated successfully', 'success');
        loadReportMetrics();
        loadReportData();
        
        // Update charts with new data
        if (attendanceChart) {
            attendanceChart.data.datasets[0].data = [
                Math.floor(Math.random() * 10) + 85,
                Math.floor(Math.random() * 10) + 85,
                Math.floor(Math.random() * 10) + 85,
                Math.floor(Math.random() * 10) + 85
            ];
            attendanceChart.update();
        }
    }, 2000);
}

// Export report
function exportReport() {
    Dashboard.showToast('Preparing report export...', 'info');
    
    setTimeout(() => {
        Dashboard.showToast('Report exported successfully', 'success');
    }, 1500);
}

// Schedule report
function scheduleReport() {
    Dashboard.showToast('Report scheduling feature coming soon', 'info');
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    initReportsPage();
    
    // Action buttons
    document.getElementById('generate-report').addEventListener('click', generateReport);
    document.getElementById('export-report').addEventListener('click', exportReport);
    document.getElementById('schedule-report').addEventListener('click', scheduleReport);
    document.getElementById('refresh-report').addEventListener('click', loadReportData);
    
    // Pagination
    document.getElementById('prev-page').addEventListener('click', () => {
        if (currentPage > 1) {
            currentPage--;
            loadReportData();
        }
    });
    
    document.getElementById('next-page').addEventListener('click', () => {
        const totalPages = Math.ceil(totalRecords / rowsPerPage);
        if (currentPage < totalPages) {
            currentPage++;
            loadReportData();
        }
    });
    
    document.getElementById('rows-per-page').addEventListener('change', (e) => {
        rowsPerPage = parseInt(e.target.value);
        currentPage = 1;
        loadReportData();
    });
});
</script>

<?= $this->endSection() ?>
