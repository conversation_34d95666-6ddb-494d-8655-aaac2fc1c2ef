<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Libraries\EasylinkSdk;
use App\Libraries\FingerspotSdk;
use App\Libraries\Fingerspot\AttendanceService;
use App\Libraries\Fingerspot\UserService;
use App\Libraries\Fingerspot\DeviceService;
use CodeIgniter\HTTP\ResponseInterface;

/**
 * Dashboard Controller
 * 
 * Modern dashboard for Fingerspot management system
 */
class DashboardController extends BaseController
{
    protected AttendanceService $attendanceService;
    protected UserService $userService;
    protected DeviceService $deviceService;

    public function __construct()
    {
        $this->attendanceService = new AttendanceService();
        $this->userService = new UserService();
        $this->deviceService = new DeviceService();
    }

    /**
     * Main dashboard page
     */
    public function index(): string
    {
        $data = [
            'title' => 'Dashboard',
            'page' => 'dashboard'
        ];

        return view('dashboard/index', $data);
    }

    /**
     * Attendance management page
     */
    public function attendance(): string
    {
        $data = [
            'title' => 'Attendance Logs',
            'page' => 'attendance'
        ];

        return view('dashboard/attendance', $data);
    }

    /**
     * User management page
     */
    public function users(): string
    {
        $data = [
            'title' => 'User Management',
            'page' => 'users'
        ];

        return view('dashboard/users', $data);
    }

    /**
     * Device management page
     */
    public function devices(): string
    {
        $data = [
            'title' => 'Device Management',
            'page' => 'devices'
        ];

        return view('dashboard/devices', $data);
    }

    /**
     * Reports page
     */
    public function reports(): string
    {
        $data = [
            'title' => 'Reports & Analytics',
            'page' => 'reports'
        ];

        return view('dashboard/reports', $data);
    }

    /**
     * Settings page
     */
    public function settings(): string
    {
        $data = [
            'title' => 'Settings',
            'page' => 'settings'
        ];

        return view('dashboard/settings', $data);
    }

    // ========================================
    // ATTENDANCE SUBMENU METHODS
    // ========================================

    /**
     * Real-time attendance monitoring
     */
    public function attendanceRealTime(): string
    {
        $data = [
            'title' => 'Real-time Attendance Monitor',
            'page' => 'attendance-realtime'
        ];

        return view('dashboard/attendance_realtime', $data);
    }

    /**
     * Attendance reports
     */
    public function attendanceReports(): string
    {
        $data = [
            'title' => 'Attendance Reports',
            'page' => 'attendance-reports'
        ];

        return view('dashboard/attendance_reports', $data);
    }

    // ========================================
    // USER MANAGEMENT SUBMENU METHODS
    // ========================================

    /**
     * User groups management
     */
    public function userGroups(): string
    {
        $data = [
            'title' => 'User Groups',
            'page' => 'user-groups'
        ];

        return view('dashboard/user_groups', $data);
    }

    /**
     * Biometric setup
     */
    public function userBiometric(): string
    {
        $data = [
            'title' => 'Biometric Setup',
            'page' => 'user-biometric'
        ];

        return view('dashboard/user_biometric', $data);
    }

    /**
     * Bulk user import
     */
    public function userBulkImport(): string
    {
        $data = [
            'title' => 'Bulk Import Users',
            'page' => 'user-import'
        ];

        return view('dashboard/user_bulk_import', $data);
    }

    // ========================================
    // DEVICE MANAGEMENT SUBMENU METHODS
    // ========================================

    /**
     * Device configuration
     */
    public function deviceConfiguration(): string
    {
        $data = [
            'title' => 'Device Configuration',
            'page' => 'device-config'
        ];

        return view('dashboard/device_configuration', $data);
    }

    /**
     * Device maintenance
     */
    public function deviceMaintenance(): string
    {
        $data = [
            'title' => 'Device Maintenance',
            'page' => 'device-maintenance'
        ];

        return view('dashboard/device_maintenance', $data);
    }

    /**
     * Device firmware management
     */
    public function deviceFirmware(): string
    {
        $data = [
            'title' => 'Firmware Management',
            'page' => 'device-firmware'
        ];

        return view('dashboard/device_firmware', $data);
    }

    // ========================================
    // REPORTS SUBMENU METHODS
    // ========================================

    /**
     * Custom reports
     */
    public function customReports(): string
    {
        $data = [
            'title' => 'Custom Reports',
            'page' => 'custom-reports'
        ];

        return view('dashboard/custom_reports', $data);
    }

    /**
     * Analytics dashboard
     */
    public function analytics(): string
    {
        $data = [
            'title' => 'Analytics Dashboard',
            'page' => 'analytics'
        ];

        return view('dashboard/analytics', $data);
    }

    /**
     * Export management
     */
    public function exports(): string
    {
        $data = [
            'title' => 'Export Management',
            'page' => 'exports'
        ];

        return view('dashboard/exports', $data);
    }

    // ========================================
    // API & INTEGRATION SUBMENU METHODS
    // ========================================

    /**
     * API endpoints management
     */
    public function apiEndpoints(): string
    {
        $data = [
            'title' => 'API Endpoints',
            'page' => 'api-endpoints'
        ];

        return view('dashboard/api_endpoints', $data);
    }

    /**
     * Webhooks management
     */
    public function webhooks(): string
    {
        $data = [
            'title' => 'Webhooks',
            'page' => 'webhooks'
        ];

        return view('dashboard/webhooks', $data);
    }

    /**
     * API logs
     */
    public function apiLogs(): string
    {
        $data = [
            'title' => 'API Logs',
            'page' => 'api-logs'
        ];

        return view('dashboard/api_logs', $data);
    }

    // ========================================
    // SETTINGS SUBMENU METHODS
    // ========================================

    /**
     * System settings
     */
    public function systemSettings(): string
    {
        $data = [
            'title' => 'System Settings',
            'page' => 'system-settings'
        ];

        return view('dashboard/system_settings', $data);
    }

    /**
     * Security settings
     */
    public function securitySettings(): string
    {
        $data = [
            'title' => 'Security Settings',
            'page' => 'security-settings'
        ];

        return view('dashboard/security_settings', $data);
    }

    /**
     * Backup settings
     */
    public function backupSettings(): string
    {
        $data = [
            'title' => 'Backup & Restore',
            'page' => 'backup-settings'
        ];

        return view('dashboard/backup_settings', $data);
    }



    /**
     * Attendance reports
     */
    public function attendanceReports(): string
    {
        $data = [
            'title' => 'Attendance Reports',
            'page' => 'attendance-reports'
        ];

        return view('dashboard/attendance_reports', $data);
    }

    /**
     * User groups management
     */
    public function userGroups(): string
    {
        $data = [
            'title' => 'User Groups',
            'page' => 'user-groups'
        ];

        return view('dashboard/user_groups', $data);
    }

    /**
     * Biometric setup
     */
    public function userBiometric(): string
    {
        $data = [
            'title' => 'Biometric Setup',
            'page' => 'user-biometric'
        ];

        return view('dashboard/user_biometric', $data);
    }

    /**
     * Bulk user import
     */
    public function userBulkImport(): string
    {
        $data = [
            'title' => 'Bulk Import Users',
            'page' => 'user-import'
        ];

        return view('dashboard/user_bulk_import', $data);
    }

    /**
     * Device configuration
     */
    public function deviceConfiguration(): string
    {
        $data = [
            'title' => 'Device Configuration',
            'page' => 'device-config'
        ];

        return view('dashboard/device_configuration', $data);
    }

    /**
     * Device maintenance
     */
    public function deviceMaintenance(): string
    {
        $data = [
            'title' => 'Device Maintenance',
            'page' => 'device-maintenance'
        ];

        return view('dashboard/device_maintenance', $data);
    }

    /**
     * Device firmware update
     */
    public function deviceFirmware(): string
    {
        $data = [
            'title' => 'Firmware Update',
            'page' => 'device-firmware'
        ];

        return view('dashboard/device_firmware', $data);
    }

    /**
     * Custom reports
     */
    public function customReports(): string
    {
        $data = [
            'title' => 'Custom Reports',
            'page' => 'custom-reports'
        ];

        return view('dashboard/custom_reports', $data);
    }

    /**
     * Analytics dashboard
     */
    public function analytics(): string
    {
        $data = [
            'title' => 'Analytics',
            'page' => 'analytics'
        ];

        return view('dashboard/analytics', $data);
    }

    /**
     * Data exports
     */
    public function exports(): string
    {
        $data = [
            'title' => 'Export Data',
            'page' => 'exports'
        ];

        return view('dashboard/exports', $data);
    }

    /**
     * API endpoints management
     */
    public function apiEndpoints(): string
    {
        $data = [
            'title' => 'API Endpoints',
            'page' => 'api-endpoints'
        ];

        return view('dashboard/api_endpoints', $data);
    }

    /**
     * Webhooks management
     */
    public function webhooks(): string
    {
        $data = [
            'title' => 'Webhooks',
            'page' => 'webhooks'
        ];

        return view('dashboard/webhooks', $data);
    }

    /**
     * API logs
     */
    public function apiLogs(): string
    {
        $data = [
            'title' => 'API Logs',
            'page' => 'api-logs'
        ];

        return view('dashboard/api_logs', $data);
    }

    /**
     * System settings
     */
    public function systemSettings(): string
    {
        $data = [
            'title' => 'System Configuration',
            'page' => 'system-config'
        ];

        return view('dashboard/system_settings', $data);
    }

    /**
     * Security settings
     */
    public function securitySettings(): string
    {
        $data = [
            'title' => 'Security Settings',
            'page' => 'security'
        ];

        return view('dashboard/security_settings', $data);
    }

    /**
     * Backup settings
     */
    public function backupSettings(): string
    {
        $data = [
            'title' => 'Backup & Restore',
            'page' => 'backup'
        ];

        return view('dashboard/backup_settings', $data);
    }

    /**
     * API: Get dashboard statistics
     */
    public function getStats(): ResponseInterface
    {
        try {
            // Get device info
            $deviceInfo = $this->deviceService->getDeviceInfo();
            
            // Get recent attendance logs
            $recentLogs = $this->attendanceService->getNewLogs();
            
            // Calculate statistics
            $stats = [
                'device_status' => $this->getDeviceStatusFromInfo($deviceInfo),
                'total_logs_today' => $this->getTodayLogsCount($recentLogs),
                'total_users' => $this->getUsersCount(),
                'last_sync' => date('Y-m-d H:i:s'),
                'device_info' => $deviceInfo
            ];

            return $this->response->setJSON([
                'success' => true,
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to fetch statistics: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * API: Get recent attendance logs
     */
    public function getRecentLogs(): ResponseInterface
    {
        try {
            $logs = $this->attendanceService->getNewLogs();
            
            return $this->response->setJSON([
                'success' => true,
                'data' => $logs
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to fetch recent logs: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * API: Get device status
     */
    public function getDeviceStatus(): ResponseInterface
    {
        try {
            $deviceInfo = $this->deviceService->getDeviceInfo();
            $status = $this->getDeviceStatusFromInfo($deviceInfo);
            
            return $this->response->setJSON([
                'success' => true,
                'data' => [
                    'status' => $status,
                    'info' => $deviceInfo,
                    'last_check' => date('Y-m-d H:i:s')
                ]
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to fetch device status: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * API: Get user count
     */
    public function getUserCount(): ResponseInterface
    {
        try {
            $count = $this->getUsersCount();
            
            return $this->response->setJSON([
                'success' => true,
                'data' => [
                    'total_users' => $count,
                    'last_updated' => date('Y-m-d H:i:s')
                ]
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to fetch user count: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Helper: Extract device status from device info
     */
    private function getDeviceStatusFromInfo(array $deviceInfo): string
    {
        // This is a simplified status check - adjust based on actual API response structure
        if (isset($deviceInfo['status'])) {
            return $deviceInfo['status'];
        }
        
        if (isset($deviceInfo['success']) && $deviceInfo['success']) {
            return 'online';
        }
        
        return 'offline';
    }

    /**
     * Helper: Count today's logs
     */
    private function getTodayLogsCount(array $logs): int
    {
        if (!isset($logs['data']) || !is_array($logs['data'])) {
            return 0;
        }
        
        $today = date('Y-m-d');
        $count = 0;
        
        foreach ($logs['data'] as $log) {
            if (isset($log['date']) && strpos($log['date'], $today) === 0) {
                $count++;
            }
        }
        
        return $count;
    }

    /**
     * Helper: Get total users count
     */
    private function getUsersCount(): int
    {
        try {
            $users = $this->userService->getAllUsers();
            
            if (isset($users['data']) && is_array($users['data'])) {
                return count($users['data']);
            }
            
            return 0;
        } catch (\Exception $e) {
            return 0;
        }
    }
}
