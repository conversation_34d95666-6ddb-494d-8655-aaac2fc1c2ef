<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */
$routes->get('/', 'Home::index');

// Fingerspot API Routes
$routes->group('api/fingerspot', function($routes) {
    // Device routes
    $routes->get('device/info', 'FingerspotController::deviceInfo');
    $routes->post('device/restart', 'FingerspotController::restartDevice');
    $routes->post('device/time', 'FingerspotController::setDeviceTime');
    $routes->get('device/test', 'FingerspotController::testConnection');

    // Attendance routes
    $routes->get('attendance/logs', 'FingerspotController::attendanceLogs');
    $routes->get('attendance/new', 'FingerspotController::newAttendanceLogs');
    $routes->get('attendance/date', 'FingerspotController::attendanceLogsByDate');

    // User routes
    $routes->get('users', 'FingerspotController::users');
    $routes->get('users/(:segment)', 'FingerspotController::userInfo/$1');
    $routes->post('users', 'FingerspotController::createUser');
    $routes->put('users/(:segment)', 'FingerspotController::updateUser/$1');
    $routes->delete('users/(:segment)', 'FingerspotController::deleteUser/$1');
});

// Fingerspot Examples Routes
$routes->group('examples/fingerspot', function($routes) {
    $routes->get('basic', 'FingerspotExampleController::basicExample');
    $routes->get('device-info', 'FingerspotExampleController::getDeviceInfo');
    $routes->get('attendance-filter', 'FingerspotExampleController::getAttendanceLogsWithFilter');
    $routes->get('user-management', 'FingerspotExampleController::userManagementExample');
    $routes->get('device-management', 'FingerspotExampleController::deviceManagementExample');
    $routes->get('realtime', 'FingerspotExampleController::realTimeAttendance');
    $routes->post('register-biometric', 'FingerspotExampleController::registerUserWithBiometric');
    $routes->get('statistics', 'FingerspotExampleController::getAttendanceStatistics');
    $routes->get('bulk', 'FingerspotExampleController::bulkOperations');
    $routes->get('error-handling', 'FingerspotExampleController::errorHandlingExample');
});

// Fingerspot Test Routes
$routes->group('test/fingerspot', function($routes) {
    $routes->get('/', 'FingerspotTestController::index');
    $routes->get('(:segment)', 'FingerspotTestController::testSpecific/$1');
});

// Fingerspot PDF Examples Routes (Exact PDF Documentation Examples)
$routes->group('pdf/fingerspot', function($routes) {
    $routes->get('/', 'FingerspotPdfExamplesController::allExamples');
    $routes->get('analysis', 'FingerspotPdfExamplesController::examplesWithAnalysis');
    $routes->get('device-info', 'FingerspotPdfExamplesController::deviceInfo');
    $routes->get('get-attlog', 'FingerspotPdfExamplesController::getAttlog');
    $routes->get('get-userinfo', 'FingerspotPdfExamplesController::getUserinfo');
    $routes->post('set-userinfo', 'FingerspotPdfExamplesController::setUserinfo');
    $routes->post('set-time', 'FingerspotPdfExamplesController::setTime');
    $routes->post('restart-device', 'FingerspotPdfExamplesController::restartDevice');
    $routes->post('register-online', 'FingerspotPdfExamplesController::registerOnline');
    $routes->delete('delete-user', 'FingerspotPdfExamplesController::deleteUser');
});

// Fingerspot cURL Tests (Exact cURL Implementation from PDF)
$routes->group('curl/fingerspot', function($routes) {
    $routes->get('/', 'FingerspotCurlTestController::testSuite');
    $routes->get('config', 'FingerspotCurlTestController::config');
    $routes->get('raw-test', 'FingerspotCurlTestController::rawCurlTest');
    $routes->get('get-attlog', 'FingerspotCurlTestController::getAttlog');
    $routes->get('get-attlog-parsed', 'FingerspotCurlTestController::getAttlogParsed');
    $routes->get('get-attlog-2days', 'FingerspotCurlTestController::getAttlogCurrentWeek');
    $routes->get('get-attlog-multiple', 'FingerspotCurlTestController::getAttlogMultipleDays');
    $routes->get('device-info', 'FingerspotCurlTestController::getDeviceInfo');
    $routes->get('user-info', 'FingerspotCurlTestController::getUserInfo');
    $routes->post('set-userinfo', 'FingerspotCurlTestController::setUserInfo');
    $routes->post('set-time', 'FingerspotCurlTestController::setTime');
});

// Fingerspot Multiple Days Tests (Comprehensive Date Range Handling)
$routes->group('multidays/fingerspot', function($routes) {
    $routes->get('/', 'FingerspotMultipleDaysController::testAll');
    $routes->get('capabilities', 'FingerspotMultipleDaysController::capabilities');
    $routes->get('test-multiple', 'FingerspotMultipleDaysController::testMultipleDays');
    $routes->get('test-aggregated', 'FingerspotMultipleDaysController::testAggregated');
    $routes->get('test-month', 'FingerspotMultipleDaysController::testMonth');
    $routes->get('test-week', 'FingerspotMultipleDaysController::testWeek');
    $routes->get('test-daterange', 'FingerspotMultipleDaysController::testDateRange');
    $routes->get('test-large', 'FingerspotMultipleDaysController::testLargeRange');
    $routes->get('compare-performance', 'FingerspotMultipleDaysController::comparePerformance');
});
