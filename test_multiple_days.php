<?php

/**
 * Multiple Days Date Range Test Script
 * 
 * Comprehensive testing of multiple days date range handling
 * with automatic chunking and aggregation
 */

// Include CodeIgniter bootstrap
require_once __DIR__ . '/vendor/autoload.php';

// Set up the environment
define('FCPATH', __DIR__ . '/public/');
define('SYSTEMPATH', __DIR__ . '/vendor/codeigniter4/framework/system/');
define('APPPATH', __DIR__ . '/app/');
define('WRITEPATH', __DIR__ . '/writable/');
define('ENVIRONMENT', 'development');

// Bootstrap CodeIgniter
$paths = new \Config\Paths();
require_once $paths->systemDirectory . '/Boot.php';
\CodeIgniter\Boot::bootConsole($paths);

echo "=== Multiple Days Date Range Test ===\n\n";

try {
    // Load helper
    helper('fingerspot');
    
    $curlClient = new \App\Libraries\FingerspotCurlClient();
    
    echo "Testing Multiple Days Date Range Handling:\n";
    echo "=========================================\n\n";
    
    // Test 1: 7 Days Aggregated
    echo "1. 7 Days Aggregated Test\n";
    echo "-------------------------\n";
    $startTime = microtime(true);
    $result7Days = $curlClient->getAttlogMultipleDaysAggregated('1', date('Y-m-d', strtotime('-7 days')), date('Y-m-d'));
    $endTime = microtime(true);
    $time7Days = round($endTime - $startTime, 2);
    
    echo "Date Range: " . date('Y-m-d', strtotime('-7 days')) . " to " . date('Y-m-d') . " (7 days)\n";
    echo "Success: " . ($result7Days['success'] ? 'YES' : 'NO') . "\n";
    echo "Total Records: " . ($result7Days['total_records'] ?? 0) . "\n";
    echo "Chunks Processed: " . ($result7Days['chunks_processed'] ?? 0) . "\n";
    echo "Execution Time: {$time7Days} seconds\n\n";
    
    // Test 2: Current Month
    echo "2. Current Month Test\n";
    echo "--------------------\n";
    $startTime = microtime(true);
    $resultMonth = $curlClient->getAttlogMonth('1');
    $endTime = microtime(true);
    $timeMonth = round($endTime - $startTime, 2);
    
    echo "Month: " . date('Y-m') . "\n";
    echo "Success: " . ($resultMonth['success'] ? 'YES' : 'NO') . "\n";
    echo "Total Records: " . ($resultMonth['total_records'] ?? 0) . "\n";
    echo "Chunks Processed: " . ($resultMonth['chunks_processed'] ?? 0) . "\n";
    echo "Execution Time: {$timeMonth} seconds\n\n";
    
    // Test 3: Current Week
    echo "3. Current Week Test\n";
    echo "-------------------\n";
    $startTime = microtime(true);
    $resultWeek = $curlClient->getAttlogWeek('1');
    $endTime = microtime(true);
    $timeWeek = round($endTime - $startTime, 2);
    
    echo "Week Start: " . date('Y-m-d', strtotime('monday this week')) . "\n";
    echo "Success: " . ($resultWeek['success'] ? 'YES' : 'NO') . "\n";
    echo "Total Records: " . ($resultWeek['total_records'] ?? 0) . "\n";
    echo "Chunks Processed: " . ($resultWeek['chunks_processed'] ?? 0) . "\n";
    echo "Execution Time: {$timeWeek} seconds\n\n";
    
    // Test 4: Large Range (30 days)
    echo "4. Large Range Test (30 days)\n";
    echo "-----------------------------\n";
    $startTime = microtime(true);
    $result30Days = $curlClient->getAttlogMultipleDaysAggregated('1', date('Y-m-d', strtotime('-30 days')), date('Y-m-d'));
    $endTime = microtime(true);
    $time30Days = round($endTime - $startTime, 2);
    
    echo "Date Range: " . date('Y-m-d', strtotime('-30 days')) . " to " . date('Y-m-d') . " (30 days)\n";
    echo "Success: " . ($result30Days['success'] ? 'YES' : 'NO') . "\n";
    echo "Total Records: " . ($result30Days['total_records'] ?? 0) . "\n";
    echo "Chunks Processed: " . ($result30Days['chunks_processed'] ?? 0) . "\n";
    echo "Execution Time: {$time30Days} seconds\n";
    echo "Records per Second: " . ($time30Days > 0 ? round(($result30Days['total_records'] ?? 0) / $time30Days, 2) : 0) . "\n\n";
    
    // Test 5: Date Range with Progress
    echo "5. Date Range with Progress Tracking\n";
    echo "------------------------------------\n";
    $progressLog = [];
    $startTime = microtime(true);
    $resultProgress = $curlClient->getAttlogDateRange(
        '1', 
        date('Y-m-d', strtotime('-15 days')), 
        date('Y-m-d'),
        function($progress) use (&$progressLog) {
            $progressLog[] = $progress;
            echo "Progress: {$progress['progress_percentage']}% - {$progress['current_range']} - Records: {$progress['records_so_far']}\n";
        }
    );
    $endTime = microtime(true);
    $timeProgress = round($endTime - $startTime, 2);
    
    echo "Final Result:\n";
    echo "Success: " . ($resultProgress['success'] ? 'YES' : 'NO') . "\n";
    echo "Total Records: " . ($resultProgress['total_records'] ?? 0) . "\n";
    echo "Chunks Processed: " . ($resultProgress['chunks_processed'] ?? 0) . "\n";
    echo "Execution Time: {$timeProgress} seconds\n\n";
    
    // Test 6: Helper Functions
    echo "6. Helper Functions Test\n";
    echo "-----------------------\n";
    
    $helperMonth = fingerspot_get_attlog_month('1');
    echo "Helper Month Records: " . ($helperMonth['total_records'] ?? 0) . "\n";
    
    $helperWeek = fingerspot_get_attlog_week('1');
    echo "Helper Week Records: " . ($helperWeek['total_records'] ?? 0) . "\n";
    
    $helperMultiple = fingerspot_get_attlog_multiple_days('1', date('Y-m-d', strtotime('-5 days')), date('Y-m-d'));
    echo "Helper Multiple Days Records: " . ($helperMultiple['total_records'] ?? 0) . "\n";
    
    $helperDateRange = fingerspot_get_attlog_daterange('1', date('Y-m-d', strtotime('-10 days')), date('Y-m-d'));
    echo "Helper Date Range Records: " . ($helperDateRange['total_records'] ?? 0) . "\n\n";
    
    // Test 7: Detailed Chunking Analysis
    echo "7. Detailed Chunking Analysis\n";
    echo "-----------------------------\n";
    $detailedResult = $curlClient->getAttlogMultipleDays('1', date('Y-m-d', strtotime('-10 days')), date('Y-m-d'), false);
    
    echo "Total Chunks: " . ($detailedResult['total_chunks'] ?? 0) . "\n";
    echo "Successful Chunks: " . ($detailedResult['successful_chunks'] ?? 0) . "\n";
    echo "Failed Chunks: " . ($detailedResult['failed_chunks'] ?? 0) . "\n";
    echo "Total Records: " . ($detailedResult['total_records'] ?? 0) . "\n";
    
    if (isset($detailedResult['chunks'])) {
        echo "\nChunk Details:\n";
        foreach ($detailedResult['chunks'] as $chunk) {
            echo "- Chunk {$chunk['chunk_number']}: {$chunk['date_range']} - ";
            echo "Records: {$chunk['records_count']} - ";
            echo "Success: " . ($chunk['success'] ? 'YES' : 'NO') . "\n";
        }
    }
    echo "\n";
    
    // Summary
    echo "=== Test Summary ===\n";
    $tests = [
        '7 Days' => $result7Days['success'] ?? false,
        'Current Month' => $resultMonth['success'] ?? false,
        'Current Week' => $resultWeek['success'] ?? false,
        '30 Days' => $result30Days['success'] ?? false,
        'Progress Tracking' => $resultProgress['success'] ?? false,
        'Helper Functions' => !empty($helperMonth['total_records']),
        'Detailed Chunking' => ($detailedResult['total_chunks'] ?? 0) > 0
    ];
    
    $successCount = count(array_filter($tests));
    $totalTests = count($tests);
    
    foreach ($tests as $name => $success) {
        echo "- " . $name . ": " . ($success ? "✅ SUCCESS" : "❌ FAILED") . "\n";
    }
    
    echo "\nOverall Success Rate: {$successCount}/{$totalTests} (" . round(($successCount/$totalTests)*100, 1) . "%)\n\n";
    
    echo "=== Performance Summary ===\n";
    echo "7 Days: {$time7Days}s - " . ($result7Days['total_records'] ?? 0) . " records\n";
    echo "Month: {$timeMonth}s - " . ($resultMonth['total_records'] ?? 0) . " records\n";
    echo "Week: {$timeWeek}s - " . ($resultWeek['total_records'] ?? 0) . " records\n";
    echo "30 Days: {$time30Days}s - " . ($result30Days['total_records'] ?? 0) . " records\n";
    echo "Progress: {$timeProgress}s - " . ($resultProgress['total_records'] ?? 0) . " records\n\n";
    
    echo "=== Web Test URLs ===\n";
    echo "Complete Test Suite: http://localhost:8080/multidays/fingerspot/\n";
    echo "Capabilities: http://localhost:8080/multidays/fingerspot/capabilities\n";
    echo "7 Days Aggregated: http://localhost:8080/multidays/fingerspot/test-aggregated\n";
    echo "Current Month: http://localhost:8080/multidays/fingerspot/test-month\n";
    echo "Current Week: http://localhost:8080/multidays/fingerspot/test-week\n";
    echo "Large Range: http://localhost:8080/multidays/fingerspot/test-large\n";
    echo "Performance: http://localhost:8080/multidays/fingerspot/compare-performance\n";
    echo "Interactive Test: http://localhost:8080/fingerspot_test.html\n\n";
    
    echo "=== Key Features Demonstrated ===\n";
    echo "✅ Automatic 2-day chunking for API compliance\n";
    echo "✅ Data aggregation across multiple requests\n";
    echo "✅ Progress tracking for long operations\n";
    echo "✅ Monthly and weekly data retrieval\n";
    echo "✅ Performance optimization\n";
    echo "✅ Error handling and recovery\n";
    echo "✅ Helper function integration\n";
    echo "✅ Large date range support (30+ days)\n";

} catch (Exception $e) {
    echo "CRITICAL ERROR: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n\n";
    
    echo "This might indicate:\n";
    echo "1. Network connectivity issues\n";
    echo "2. API credentials problems\n";
    echo "3. CodeIgniter framework issues\n";
    echo "4. Memory or timeout issues for large ranges\n";
}

echo "\n=== Multiple Days Test Complete ===\n";
