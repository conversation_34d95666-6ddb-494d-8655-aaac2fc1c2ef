<?= $this->extend('dashboard/layout') ?>

<?= $this->section('content') ?>

<!-- <PERSON> Header -->
<div class="mb-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Device Management</h1>
            <p class="text-gray-600 dark:text-gray-400">Monitor and control your Fingerspot devices</p>
        </div>
        <div class="flex items-center space-x-3">
            <button id="sync-all-devices" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-sync mr-2"></i>
                Sync All
            </button>
            <button id="add-device" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                <i class="fas fa-plus mr-2"></i>
                Add Device
            </button>
        </div>
    </div>
</div>

<!-- Device Status Overview -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Devices</p>
                <p id="total-devices" class="text-2xl font-bold text-gray-900 dark:text-white">--</p>
            </div>
            <div class="p-3 bg-blue-100 dark:bg-blue-900 rounded-full">
                <i class="fas fa-microchip text-blue-600 dark:text-blue-400"></i>
            </div>
        </div>
    </div>
    
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Online</p>
                <p id="online-devices" class="text-2xl font-bold text-green-600 dark:text-green-400">--</p>
            </div>
            <div class="p-3 bg-green-100 dark:bg-green-900 rounded-full">
                <i class="fas fa-wifi text-green-600 dark:text-green-400"></i>
            </div>
        </div>
    </div>
    
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Offline</p>
                <p id="offline-devices" class="text-2xl font-bold text-red-600 dark:text-red-400">--</p>
            </div>
            <div class="p-3 bg-red-100 dark:bg-red-900 rounded-full">
                <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400"></i>
            </div>
        </div>
    </div>
    
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Maintenance</p>
                <p id="maintenance-devices" class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">--</p>
            </div>
            <div class="p-3 bg-yellow-100 dark:bg-yellow-900 rounded-full">
                <i class="fas fa-tools text-yellow-600 dark:text-yellow-400"></i>
            </div>
        </div>
    </div>
</div>

<!-- Main Device Grid -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Primary Device Card -->
    <div class="lg:col-span-2 bg-white dark:bg-gray-800 rounded-xl shadow-sm">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Primary Device</h3>
                <span id="primary-device-status" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                    <i class="fas fa-circle text-xs mr-1"></i>
                    Online
                </span>
            </div>
        </div>
        
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Device Information -->
                <div>
                    <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Device Information</h4>
                    <div id="device-info" class="space-y-2">
                        <div class="flex items-center justify-center py-8">
                            <i class="fas fa-spinner loading-spinner text-gray-400 mr-2"></i>
                            <span class="text-gray-500 dark:text-gray-400">Loading device info...</span>
                        </div>
                    </div>
                </div>
                
                <!-- Device Statistics -->
                <div>
                    <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Statistics</h4>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Total Users</span>
                            <span id="device-users" class="text-sm font-medium text-gray-900 dark:text-white">--</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Total Logs</span>
                            <span id="device-logs" class="text-sm font-medium text-gray-900 dark:text-white">--</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Storage Used</span>
                            <span id="device-storage" class="text-sm font-medium text-gray-900 dark:text-white">--</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Uptime</span>
                            <span id="device-uptime" class="text-sm font-medium text-gray-900 dark:text-white">--</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Device Actions -->
            <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                    <button onclick="syncDevice()" class="flex items-center justify-center px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-sync mr-2"></i>
                        Sync
                    </button>
                    <button onclick="restartDevice()" class="flex items-center justify-center px-3 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors">
                        <i class="fas fa-power-off mr-2"></i>
                        Restart
                    </button>
                    <button onclick="clearLogs()" class="flex items-center justify-center px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                        <i class="fas fa-trash mr-2"></i>
                        Clear Logs
                    </button>
                    <button onclick="updateTime()" class="flex items-center justify-center px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                        <i class="fas fa-clock mr-2"></i>
                        Set Time
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Device Controls & Monitoring -->
    <div class="space-y-6">
        <!-- Quick Controls -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Quick Controls</h3>
            <div class="space-y-3">
                <button onclick="testConnection()" class="w-full flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                    <i class="fas fa-plug mr-2"></i>
                    Test Connection
                </button>
                <button onclick="downloadLogs()" class="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-download mr-2"></i>
                    Download Logs
                </button>
                <button onclick="backupDevice()" class="w-full flex items-center justify-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                    <i class="fas fa-save mr-2"></i>
                    Backup Device
                </button>
                <button onclick="factoryReset()" class="w-full flex items-center justify-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    Factory Reset
                </button>
            </div>
        </div>

        <!-- Network Information -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Network Information</h3>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">IP Address</span>
                    <span id="device-ip" class="text-sm font-medium text-gray-900 dark:text-white">--</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">MAC Address</span>
                    <span id="device-mac" class="text-sm font-medium text-gray-900 dark:text-white">--</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Signal Strength</span>
                    <span id="device-signal" class="text-sm font-medium text-green-600 dark:text-green-400">--</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Last Ping</span>
                    <span id="device-ping" class="text-sm text-gray-600 dark:text-gray-400">--</span>
                </div>
            </div>
        </div>

        <!-- System Health -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">System Health</h3>
            <div class="space-y-4">
                <!-- CPU Usage -->
                <div>
                    <div class="flex justify-between mb-1">
                        <span class="text-sm text-gray-600 dark:text-gray-400">CPU Usage</span>
                        <span id="cpu-usage" class="text-sm font-medium text-gray-900 dark:text-white">--</span>
                    </div>
                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div id="cpu-bar" class="bg-blue-600 h-2 rounded-full" style="width: 0%"></div>
                    </div>
                </div>
                
                <!-- Memory Usage -->
                <div>
                    <div class="flex justify-between mb-1">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Memory Usage</span>
                        <span id="memory-usage" class="text-sm font-medium text-gray-900 dark:text-white">--</span>
                    </div>
                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div id="memory-bar" class="bg-green-600 h-2 rounded-full" style="width: 0%"></div>
                    </div>
                </div>
                
                <!-- Storage Usage -->
                <div>
                    <div class="flex justify-between mb-1">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Storage Usage</span>
                        <span id="storage-usage" class="text-sm font-medium text-gray-900 dark:text-white">--</span>
                    </div>
                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div id="storage-bar" class="bg-purple-600 h-2 rounded-full" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Device Activity Log -->
<div class="mt-6 bg-white dark:bg-gray-800 rounded-xl shadow-sm">
    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Device Activity Log</h3>
            <button id="refresh-activity" class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                <i class="fas fa-sync-alt"></i>
            </button>
        </div>
    </div>
    
    <div class="p-6">
        <div id="activity-log" class="space-y-3">
            <div class="flex items-center justify-center py-8">
                <i class="fas fa-spinner loading-spinner text-gray-400 mr-2"></i>
                <span class="text-gray-500 dark:text-gray-400">Loading activity log...</span>
            </div>
        </div>
    </div>
</div>

<script>
// Device management JavaScript
let deviceStatusInterval;

// Initialize devices page
function initDevicesPage() {
    loadDeviceInfo();
    loadDeviceStats();
    loadNetworkInfo();
    loadSystemHealth();
    loadActivityLog();
    
    // Set up auto-refresh every 30 seconds
    deviceStatusInterval = setInterval(() => {
        loadDeviceStats();
        loadSystemHealth();
    }, 30000);
}

// Load device information
function loadDeviceInfo() {
    fetch('/dashboard/api/device-status')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayDeviceInfo(data.data.info);
                updateDeviceStatus(data.data.status);
            } else {
                document.getElementById('device-info').innerHTML = 
                    '<div class="text-center py-4 text-gray-500">Failed to load device info</div>';
            }
        })
        .catch(error => {
            console.error('Error loading device info:', error);
            document.getElementById('device-info').innerHTML = 
                '<div class="text-center py-4 text-gray-500">Error loading device info</div>';
        });
}

// Load device statistics
function loadDeviceStats() {
    // Simulate loading stats
    setTimeout(() => {
        document.getElementById('total-devices').textContent = '1';
        document.getElementById('online-devices').textContent = '1';
        document.getElementById('offline-devices').textContent = '0';
        document.getElementById('maintenance-devices').textContent = '0';
        
        document.getElementById('device-users').textContent = '156';
        document.getElementById('device-logs').textContent = '2,847';
        document.getElementById('device-storage').textContent = '45%';
        document.getElementById('device-uptime').textContent = '15 days';
    }, 500);
}

// Load network information
function loadNetworkInfo() {
    setTimeout(() => {
        document.getElementById('device-ip').textContent = '*************';
        document.getElementById('device-mac').textContent = '00:1B:44:11:3A:B7';
        document.getElementById('device-signal').textContent = 'Strong';
        document.getElementById('device-ping').textContent = '< 1ms';
    }, 800);
}

// Load system health
function loadSystemHealth() {
    setTimeout(() => {
        // Simulate random health data
        const cpu = Math.floor(Math.random() * 30) + 20;
        const memory = Math.floor(Math.random() * 40) + 30;
        const storage = Math.floor(Math.random() * 20) + 40;
        
        document.getElementById('cpu-usage').textContent = cpu + '%';
        document.getElementById('cpu-bar').style.width = cpu + '%';
        
        document.getElementById('memory-usage').textContent = memory + '%';
        document.getElementById('memory-bar').style.width = memory + '%';
        
        document.getElementById('storage-usage').textContent = storage + '%';
        document.getElementById('storage-bar').style.width = storage + '%';
    }, 1000);
}

// Load activity log
function loadActivityLog() {
    setTimeout(() => {
        const activities = [
            { time: '2 minutes ago', action: 'User authentication successful', type: 'success', user: 'John Doe' },
            { time: '5 minutes ago', action: 'Device sync completed', type: 'info', user: 'System' },
            { time: '12 minutes ago', action: 'New user registered', type: 'success', user: 'Jane Smith' },
            { time: '25 minutes ago', action: 'Failed authentication attempt', type: 'warning', user: 'Unknown' },
            { time: '1 hour ago', action: 'Device restart completed', type: 'info', user: 'Admin' }
        ];
        
        const activityHtml = activities.map(activity => `
            <div class="flex items-center justify-between py-3 border-b border-gray-100 dark:border-gray-700 last:border-b-0">
                <div class="flex items-center">
                    <div class="w-8 h-8 rounded-full flex items-center justify-center mr-3 ${
                        activity.type === 'success' ? 'bg-green-100 dark:bg-green-900' :
                        activity.type === 'warning' ? 'bg-yellow-100 dark:bg-yellow-900' :
                        'bg-blue-100 dark:bg-blue-900'
                    }">
                        <i class="fas ${
                            activity.type === 'success' ? 'fa-check text-green-600 dark:text-green-400' :
                            activity.type === 'warning' ? 'fa-exclamation-triangle text-yellow-600 dark:text-yellow-400' :
                            'fa-info text-blue-600 dark:text-blue-400'
                        } text-xs"></i>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-900 dark:text-white">${activity.action}</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">User: ${activity.user}</p>
                    </div>
                </div>
                <span class="text-xs text-gray-500 dark:text-gray-400">${activity.time}</span>
            </div>
        `).join('');
        
        document.getElementById('activity-log').innerHTML = activityHtml;
    }, 1200);
}

// Display device information
function displayDeviceInfo(deviceInfo) {
    const container = document.getElementById('device-info');
    
    const infoHtml = `
        <div class="space-y-2">
            <div class="flex justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">Model</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">${deviceInfo.model || 'Fingerspot Pro'}</span>
            </div>
            <div class="flex justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">Serial Number</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">${deviceInfo.serial || 'FS-2024-001'}</span>
            </div>
            <div class="flex justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">Firmware</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">${deviceInfo.firmware || 'v2.1.5'}</span>
            </div>
            <div class="flex justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">Cloud ID</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">${deviceInfo.cloudId || 'C2630450C3233D26'}</span>
            </div>
            <div class="flex justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">Last Sync</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">${new Date().toLocaleTimeString()}</span>
            </div>
        </div>
    `;
    
    container.innerHTML = infoHtml;
}

// Update device status
function updateDeviceStatus(status) {
    const statusElement = document.getElementById('primary-device-status');
    const statusClasses = {
        'online': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
        'offline': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
        'maintenance': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
    };
    
    statusElement.className = `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusClasses[status] || statusClasses.offline}`;
    statusElement.innerHTML = `<i class="fas fa-circle text-xs mr-1"></i>${status.charAt(0).toUpperCase() + status.slice(1)}`;
}

// Device action functions
function syncDevice() {
    Dashboard.showLoading();
    Dashboard.showToast('Synchronizing device...', 'info');
    
    setTimeout(() => {
        Dashboard.hideLoading();
        Dashboard.showToast('Device synchronized successfully', 'success');
        loadDeviceStats();
    }, 3000);
}

function restartDevice() {
    if (confirm('Are you sure you want to restart the device? This may take a few minutes and will temporarily interrupt service.')) {
        Dashboard.showLoading();
        Dashboard.showToast('Restarting device...', 'warning');
        
        setTimeout(() => {
            Dashboard.hideLoading();
            Dashboard.showToast('Device restart initiated. Please wait for the device to come back online.', 'success');
            updateDeviceStatus('maintenance');
            
            // Simulate device coming back online
            setTimeout(() => {
                updateDeviceStatus('online');
                Dashboard.showToast('Device is back online', 'success');
            }, 30000);
        }, 2000);
    }
}

function clearLogs() {
    if (confirm('Are you sure you want to clear all attendance logs? This action cannot be undone.')) {
        Dashboard.showLoading();
        
        setTimeout(() => {
            Dashboard.hideLoading();
            Dashboard.showToast('Attendance logs cleared successfully', 'success');
            loadDeviceStats();
        }, 2000);
    }
}

function updateTime() {
    Dashboard.showLoading();
    Dashboard.showToast('Updating device time...', 'info');
    
    setTimeout(() => {
        Dashboard.hideLoading();
        Dashboard.showToast('Device time updated successfully', 'success');
    }, 1500);
}

function testConnection() {
    Dashboard.showToast('Testing connection...', 'info');
    
    setTimeout(() => {
        Dashboard.showToast('Connection test successful - Device is reachable', 'success');
    }, 2000);
}

function downloadLogs() {
    Dashboard.showToast('Preparing log download...', 'info');
    
    setTimeout(() => {
        Dashboard.showToast('Log download started', 'success');
    }, 1500);
}

function backupDevice() {
    Dashboard.showLoading();
    Dashboard.showToast('Creating device backup...', 'info');
    
    setTimeout(() => {
        Dashboard.hideLoading();
        Dashboard.showToast('Device backup completed successfully', 'success');
    }, 4000);
}

function factoryReset() {
    if (confirm('WARNING: This will reset the device to factory settings and erase ALL data including users and logs. Are you absolutely sure?')) {
        if (confirm('This action is IRREVERSIBLE. Type "RESET" to confirm.')) {
            Dashboard.showLoading();
            Dashboard.showToast('Performing factory reset...', 'warning');
            
            setTimeout(() => {
                Dashboard.hideLoading();
                Dashboard.showToast('Factory reset completed. Device will restart automatically.', 'success');
                updateDeviceStatus('maintenance');
            }, 5000);
        }
    }
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    initDevicesPage();
    
    // Refresh activity log
    document.getElementById('refresh-activity').addEventListener('click', loadActivityLog);
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (deviceStatusInterval) {
        clearInterval(deviceStatusInterval);
    }
});
</script>

<?= $this->endSection() ?>
