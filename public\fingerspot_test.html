<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fingerspot API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            border-left: 4px solid #28a745;
        }
        .error {
            border-left: 4px solid #dc3545;
        }
        .loading {
            color: #6c757d;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        h2 {
            color: #495057;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        .status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>🔍 Fingerspot API Test Dashboard</h1>

    <div class="container">
        <h2>📋 Test Overview</h2>
        <p>This page tests the Fingerspot API implementation using <strong>exact examples from the PDF documentation</strong>.</p>
        <p>The PDF examples section below tests the exact API calls as documented in the Fingerspot developer PDF.</p>
        <p><strong>Server:</strong> <span id="server-status">Checking...</span></p>
        <p><strong>Recommended:</strong> Start with the "🔥 All PDF Examples" button below for comprehensive testing.</p>
    </div>

    <div class="container">
        <h2>🔥 Exact cURL Implementation (PDF)</h2>
        <p><strong>These tests use the EXACT cURL code from your PDF example with environment variables</strong></p>
        <p><strong>⚠️ API Limitation:</strong> Date range cannot exceed 2 days per request</p>
        <button class="test-button" onclick="testEndpoint('/curl/fingerspot/raw-test', 'curl-raw-test')">
            ⚡ Raw cURL Test (Exact PDF Code)
        </button>
        <button class="test-button" onclick="testEndpoint('/curl/fingerspot/', 'curl-test-suite')">
            🧪 Complete cURL Test Suite
        </button>
        <button class="test-button" onclick="testEndpoint('/curl/fingerspot/get-attlog', 'curl-get-attlog')">
            📝 Get Attlog (Max 2 Days)
        </button>
        <button class="test-button" onclick="testEndpoint('/curl/fingerspot/get-attlog-2days', 'curl-get-attlog-2days')">
            📅 Get Attlog (Last 2 Days)
        </button>
        <button class="test-button" onclick="testEndpoint('/curl/fingerspot/get-attlog-multiple', 'curl-get-attlog-multiple')">
            📊 Get Attlog (Multiple Days - Chunked)
        </button>
        <button class="test-button" onclick="testEndpoint('/curl/fingerspot/config', 'curl-config')">
            ⚙️ cURL Configuration
        </button>
        <div id="curl-raw-test" class="result" style="display: none;"></div>
        <div id="curl-test-suite" class="result" style="display: none;"></div>
        <div id="curl-get-attlog" class="result" style="display: none;"></div>
        <div id="curl-get-attlog-2days" class="result" style="display: none;"></div>
        <div id="curl-get-attlog-multiple" class="result" style="display: none;"></div>
        <div id="curl-config" class="result" style="display: none;"></div>
    </div>

    <div class="container">
        <h2>📅 Multiple Days Date Range Handling</h2>
        <p><strong>Comprehensive multiple days support with automatic chunking and aggregation</strong></p>
        <button class="test-button" onclick="testEndpoint('/multidays/fingerspot/', 'multidays-all-tests')">
            🚀 All Multiple Days Tests
        </button>
        <button class="test-button" onclick="testEndpoint('/multidays/fingerspot/capabilities', 'multidays-capabilities')">
            ⚙️ Multiple Days Capabilities
        </button>
        <button class="test-button" onclick="testEndpoint('/multidays/fingerspot/test-aggregated', 'multidays-aggregated')">
            📊 7 Days Aggregated
        </button>
        <button class="test-button" onclick="testEndpoint('/multidays/fingerspot/test-month', 'multidays-month')">
            📅 Current Month
        </button>
        <button class="test-button" onclick="testEndpoint('/multidays/fingerspot/test-week', 'multidays-week')">
            📆 Current Week
        </button>
        <button class="test-button" onclick="testEndpoint('/multidays/fingerspot/test-large', 'multidays-large')">
            📈 Large Range (30 Days)
        </button>
        <button class="test-button" onclick="testEndpoint('/multidays/fingerspot/compare-performance', 'multidays-performance')">
            ⚡ Performance Comparison
        </button>
        <div id="multidays-all-tests" class="result" style="display: none;"></div>
        <div id="multidays-capabilities" class="result" style="display: none;"></div>
        <div id="multidays-aggregated" class="result" style="display: none;"></div>
        <div id="multidays-month" class="result" style="display: none;"></div>
        <div id="multidays-week" class="result" style="display: none;"></div>
        <div id="multidays-large" class="result" style="display: none;"></div>
        <div id="multidays-performance" class="result" style="display: none;"></div>
    </div>

    <div class="container">
        <h2>📋 PDF Documentation Examples</h2>
        <p><strong>These tests use the exact examples from the PDF documentation</strong></p>
        <button class="test-button" onclick="testEndpoint('/pdf/fingerspot/', 'pdf-all-examples')">
            🔥 All PDF Examples
        </button>
        <button class="test-button" onclick="testEndpoint('/pdf/fingerspot/analysis', 'pdf-analysis')">
            📊 PDF Examples Analysis
        </button>
        <button class="test-button" onclick="testEndpoint('/pdf/fingerspot/device-info', 'pdf-device-info')">
            📱 Device Info (PDF)
        </button>
        <button class="test-button" onclick="testEndpoint('/pdf/fingerspot/get-attlog', 'pdf-get-attlog')">
            📝 Get Attlog (PDF)
        </button>
        <button class="test-button" onclick="testEndpoint('/pdf/fingerspot/get-userinfo', 'pdf-get-userinfo')">
            👤 Get Userinfo (PDF)
        </button>
        <div id="pdf-all-examples" class="result" style="display: none;"></div>
        <div id="pdf-analysis" class="result" style="display: none;"></div>
        <div id="pdf-device-info" class="result" style="display: none;"></div>
        <div id="pdf-get-attlog" class="result" style="display: none;"></div>
        <div id="pdf-get-userinfo" class="result" style="display: none;"></div>
    </div>

    <div class="container">
        <h2>🧪 Basic Tests</h2>
        <button class="test-button" onclick="testEndpoint('/test/fingerspot/', 'basic-test')">
            Run Complete Test Suite
        </button>
        <button class="test-button" onclick="testEndpoint('/examples/fingerspot/basic', 'basic-example')">
            Basic Example (EasylinkSdk)
        </button>
        <div id="basic-test" class="result" style="display: none;"></div>
        <div id="basic-example" class="result" style="display: none;"></div>
    </div>

    <div class="container">
        <h2>🔧 Device Tests</h2>
        <button class="test-button" onclick="testEndpoint('/api/fingerspot/device/info', 'device-info')">
            Device Info
        </button>
        <button class="test-button" onclick="testEndpoint('/api/fingerspot/device/test', 'device-test')">
            Test Connection
        </button>
        <button class="test-button" onclick="testEndpoint('/test/fingerspot/device', 'device-specific')">
            Device Specific Tests
        </button>
        <div id="device-info" class="result" style="display: none;"></div>
        <div id="device-test" class="result" style="display: none;"></div>
        <div id="device-specific" class="result" style="display: none;"></div>
    </div>

    <div class="container">
        <h2>👥 User Tests</h2>
        <button class="test-button" onclick="testEndpoint('/api/fingerspot/users', 'users-list')">
            Get All Users
        </button>
        <button class="test-button" onclick="testEndpoint('/test/fingerspot/user', 'user-specific')">
            User Specific Tests
        </button>
        <div id="users-list" class="result" style="display: none;"></div>
        <div id="user-specific" class="result" style="display: none;"></div>
    </div>

    <div class="container">
        <h2>📊 Attendance Tests</h2>
        <button class="test-button" onclick="testEndpoint('/api/fingerspot/attendance/logs', 'attendance-logs')">
            Get Attendance Logs
        </button>
        <button class="test-button" onclick="testEndpoint('/api/fingerspot/attendance/new', 'attendance-new')">
            Get New Logs
        </button>
        <button class="test-button" onclick="testEndpoint('/test/fingerspot/attendance', 'attendance-specific')">
            Attendance Specific Tests
        </button>
        <div id="attendance-logs" class="result" style="display: none;"></div>
        <div id="attendance-new" class="result" style="display: none;"></div>
        <div id="attendance-specific" class="result" style="display: none;"></div>
    </div>

    <div class="container">
        <h2>📚 Examples</h2>
        <button class="test-button" onclick="testEndpoint('/examples/fingerspot/device-info', 'example-device')">
            Device Info Example
        </button>
        <button class="test-button" onclick="testEndpoint('/examples/fingerspot/user-management', 'example-user')">
            User Management Example
        </button>
        <button class="test-button" onclick="testEndpoint('/examples/fingerspot/error-handling', 'example-error')">
            Error Handling Example
        </button>
        <div id="example-device" class="result" style="display: none;"></div>
        <div id="example-user" class="result" style="display: none;"></div>
        <div id="example-error" class="result" style="display: none;"></div>
    </div>

    <script>
        // Check server status on load
        window.onload = function() {
            checkServerStatus();
        };

        function checkServerStatus() {
            fetch('/')
                .then(response => {
                    if (response.ok) {
                        document.getElementById('server-status').innerHTML = 
                            '<span class="status success">✅ Online</span>';
                    } else {
                        document.getElementById('server-status').innerHTML = 
                            '<span class="status error">❌ Error</span>';
                    }
                })
                .catch(error => {
                    document.getElementById('server-status').innerHTML = 
                        '<span class="status error">❌ Offline</span>';
                });
        }

        function testEndpoint(url, resultId) {
            const resultDiv = document.getElementById(resultId);
            resultDiv.style.display = 'block';
            resultDiv.className = 'result loading';
            resultDiv.textContent = 'Loading...';

            fetch(url)
                .then(response => {
                    return response.text().then(text => {
                        try {
                            const json = JSON.parse(text);
                            return {
                                ok: response.ok,
                                status: response.status,
                                data: json
                            };
                        } catch (e) {
                            return {
                                ok: response.ok,
                                status: response.status,
                                data: text
                            };
                        }
                    });
                })
                .then(result => {
                    resultDiv.className = result.ok ? 'result success' : 'result error';
                    
                    let output = `Status: ${result.status}\n`;
                    output += `URL: ${url}\n`;
                    output += `Time: ${new Date().toLocaleTimeString()}\n\n`;
                    
                    if (typeof result.data === 'object') {
                        output += JSON.stringify(result.data, null, 2);
                    } else {
                        output += result.data;
                    }
                    
                    resultDiv.textContent = output;
                })
                .catch(error => {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `Error: ${error.message}\nURL: ${url}\nTime: ${new Date().toLocaleTimeString()}`;
                });
        }
    </script>
</body>
</html>
