<?php

/**
 * Fingerspot Helper Functions
 * 
 * Helper functions to make Fingerspot API usage even easier
 */

if (!function_exists('fingerspot_device')) {
    /**
     * Get device information
     * 
     * @return array
     */
    function fingerspot_device(): array
    {
        return \App\Libraries\FingerspotSdk::device();
    }
}

if (!function_exists('fingerspot_scanlog')) {
    /**
     * Get attendance logs
     * 
     * @param array $params Optional parameters
     * @return array
     */
    function fingerspot_scanlog(array $params = []): array
    {
        return \App\Libraries\FingerspotSdk::scanlog($params);
    }
}

if (!function_exists('fingerspot_scanlog_new')) {
    /**
     * Get new attendance logs
     * 
     * @param string|null $lastScanId Last scan ID
     * @return array
     */
    function fingerspot_scanlog_new(?string $lastScanId = null): array
    {
        return \App\Libraries\FingerspotSdk::scanlogNew($lastScanId);
    }
}

if (!function_exists('fingerspot_user_info')) {
    /**
     * Get user information
     * 
     * @param string|null $userPin User PIN, null for all users
     * @return array
     */
    function fingerspot_user_info(?string $userPin = null): array
    {
        return \App\Libraries\FingerspotSdk::userInfo($userPin);
    }
}

if (!function_exists('fingerspot_set_user')) {
    /**
     * Set/Add user to device
     * 
     * @param array $userData User data
     * @return array
     */
    function fingerspot_set_user(array $userData): array
    {
        return \App\Libraries\FingerspotSdk::setUser($userData);
    }
}

if (!function_exists('fingerspot_delete_user')) {
    /**
     * Delete user from device
     * 
     * @param string $userPin User PIN
     * @return array
     */
    function fingerspot_delete_user(string $userPin): array
    {
        return \App\Libraries\FingerspotSdk::deleteUser($userPin);
    }
}

if (!function_exists('fingerspot_restart_device')) {
    /**
     * Restart device
     * 
     * @return array
     */
    function fingerspot_restart_device(): array
    {
        return \App\Libraries\FingerspotSdk::restartDevice();
    }
}

if (!function_exists('fingerspot_set_time')) {
    /**
     * Set device time
     * 
     * @param string|null $datetime Datetime string
     * @param string|null $timezone Timezone string
     * @return array
     */
    function fingerspot_set_time(?string $datetime = null, ?string $timezone = null): array
    {
        return \App\Libraries\FingerspotSdk::setTime($datetime, $timezone);
    }
}

if (!function_exists('fingerspot_response')) {
    /**
     * Create FingerspotResponse object from array
     * 
     * @param array $response Response array
     * @return \App\Models\FingerspotResponse
     */
    function fingerspot_response(array $response): \App\Models\FingerspotResponse
    {
        return new \App\Models\FingerspotResponse($response);
    }
}

if (!function_exists('fingerspot_is_success')) {
    /**
     * Check if response is successful
     * 
     * @param array $response Response array
     * @return bool
     */
    function fingerspot_is_success(array $response): bool
    {
        return $response['success'] ?? false;
    }
}

if (!function_exists('fingerspot_get_data')) {
    /**
     * Get data from response
     * 
     * @param array $response Response array
     * @param mixed $default Default value if no data
     * @return mixed
     */
    function fingerspot_get_data(array $response, mixed $default = null): mixed
    {
        return $response['data'] ?? $default;
    }
}

if (!function_exists('fingerspot_get_message')) {
    /**
     * Get message from response
     *
     * @param array $response Response array
     * @return string
     */
    function fingerspot_get_message(array $response): string
    {
        return $response['message'] ?? '';
    }
}

if (!function_exists('fingerspot_get_attlog_curl')) {
    /**
     * Get attendance logs using exact cURL implementation from PDF
     * Note: API allows maximum 2 days date range per request
     *
     * @param string $transId Transaction ID
     * @param string|null $startDate Start date (Y-m-d format)
     * @param string|null $endDate End date (Y-m-d format)
     * @return string Raw response
     */
    function fingerspot_get_attlog_curl(string $transId = "1", ?string $startDate = null, ?string $endDate = null): string
    {
        $curlClient = new \App\Libraries\FingerspotCurlClient();
        return $curlClient->getAttlog($transId, $startDate, $endDate);
    }
}

if (!function_exists('fingerspot_get_attlog_curl_parsed')) {
    /**
     * Get attendance logs using exact cURL implementation with parsed response
     * Note: API allows maximum 2 days date range per request
     *
     * @param string $transId Transaction ID
     * @param string|null $startDate Start date (Y-m-d format)
     * @param string|null $endDate End date (Y-m-d format)
     * @return array Parsed response
     */
    function fingerspot_get_attlog_curl_parsed(string $transId = "1", ?string $startDate = null, ?string $endDate = null): array
    {
        $curlClient = new \App\Libraries\FingerspotCurlClient();
        return $curlClient->getAttlogParsed($transId, $startDate, $endDate);
    }
}

if (!function_exists('fingerspot_get_attlog_multiple_days')) {
    /**
     * Get attendance logs for multiple days (handles API 2-day limit by chunking)
     *
     * @param string $transId Transaction ID
     * @param string|null $startDate Start date (Y-m-d format)
     * @param string|null $endDate End date (Y-m-d format)
     * @param bool $aggregateData Whether to combine all data into single array
     * @return array Combined results from multiple API calls
     */
    function fingerspot_get_attlog_multiple_days(string $transId = "1", ?string $startDate = null, ?string $endDate = null, bool $aggregateData = true): array
    {
        $curlClient = new \App\Libraries\FingerspotCurlClient();
        if ($aggregateData) {
            return $curlClient->getAttlogMultipleDaysAggregated($transId, $startDate, $endDate);
        }
        return $curlClient->getAttlogMultipleDays($transId, $startDate, $endDate, false);
    }
}

if (!function_exists('fingerspot_get_attlog_month')) {
    /**
     * Get attendance logs for a specific month
     *
     * @param string $transId Transaction ID
     * @param string|null $month Month in Y-m format (e.g., "2024-01")
     * @return array Monthly attendance data
     */
    function fingerspot_get_attlog_month(string $transId = "1", ?string $month = null): array
    {
        $curlClient = new \App\Libraries\FingerspotCurlClient();
        return $curlClient->getAttlogMonth($transId, $month);
    }
}

if (!function_exists('fingerspot_get_attlog_week')) {
    /**
     * Get attendance logs for a specific week
     *
     * @param string $transId Transaction ID
     * @param string|null $weekStartDate Start date of week (Y-m-d format)
     * @return array Weekly attendance data
     */
    function fingerspot_get_attlog_week(string $transId = "1", ?string $weekStartDate = null): array
    {
        $curlClient = new \App\Libraries\FingerspotCurlClient();
        return $curlClient->getAttlogWeek($transId, $weekStartDate);
    }
}

if (!function_exists('fingerspot_get_attlog_daterange')) {
    /**
     * Get attendance logs for date range with automatic chunking
     *
     * @param string $transId Transaction ID
     * @param string|null $startDate Start date (Y-m-d format)
     * @param string|null $endDate End date (Y-m-d format)
     * @return array Date range attendance data
     */
    function fingerspot_get_attlog_daterange(string $transId = "1", ?string $startDate = null, ?string $endDate = null): array
    {
        $curlClient = new \App\Libraries\FingerspotCurlClient();
        return $curlClient->getAttlogDateRange($transId, $startDate, $endDate);
    }
}
