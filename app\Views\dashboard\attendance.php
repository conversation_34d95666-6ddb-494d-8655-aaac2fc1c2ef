<?= $this->extend('dashboard/layout') ?>

<?= $this->section('content') ?>

<!-- <PERSON> Header -->
<div class="mb-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Attendance Logs</h1>
            <p class="text-gray-600 dark:text-gray-400">Monitor and manage employee attendance records</p>
        </div>
        <div class="flex items-center space-x-3">
            <button id="export-logs" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                <i class="fas fa-download mr-2"></i>
                Export
            </button>
            <button id="sync-attendance" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-sync mr-2"></i>
                Sync Now
            </button>
        </div>
    </div>
</div>

<!-- Filters and Search -->
<div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 mb-6">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <!-- Date Range -->
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Date Range</label>
            <div class="flex space-x-2">
                <input type="date" id="start-date" class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                <input type="date" id="end-date" class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
            </div>
        </div>
        
        <!-- User Search -->
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Search User</label>
            <input type="text" id="user-search" placeholder="Name or PIN" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
        </div>
        
        <!-- Department Filter -->
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Department</label>
            <select id="department-filter" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                <option value="">All Departments</option>
                <option value="IT">IT</option>
                <option value="HR">HR</option>
                <option value="Finance">Finance</option>
                <option value="Operations">Operations</option>
            </select>
        </div>
        
        <!-- Actions -->
        <div class="flex items-end">
            <button id="apply-filters" class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-search mr-2"></i>
                Apply Filters
            </button>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Logs</p>
                <p id="total-logs" class="text-2xl font-bold text-gray-900 dark:text-white">--</p>
            </div>
            <div class="p-3 bg-blue-100 dark:bg-blue-900 rounded-full">
                <i class="fas fa-list text-blue-600 dark:text-blue-400"></i>
            </div>
        </div>
    </div>
    
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Present Today</p>
                <p id="present-today" class="text-2xl font-bold text-green-600 dark:text-green-400">--</p>
            </div>
            <div class="p-3 bg-green-100 dark:bg-green-900 rounded-full">
                <i class="fas fa-user-check text-green-600 dark:text-green-400"></i>
            </div>
        </div>
    </div>
    
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Late Arrivals</p>
                <p id="late-arrivals" class="text-2xl font-bold text-orange-600 dark:text-orange-400">--</p>
            </div>
            <div class="p-3 bg-orange-100 dark:bg-orange-900 rounded-full">
                <i class="fas fa-clock text-orange-600 dark:text-orange-400"></i>
            </div>
        </div>
    </div>
    
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Early Departures</p>
                <p id="early-departures" class="text-2xl font-bold text-red-600 dark:text-red-400">--</p>
            </div>
            <div class="p-3 bg-red-100 dark:bg-red-900 rounded-full">
                <i class="fas fa-sign-out-alt text-red-600 dark:text-red-400"></i>
            </div>
        </div>
    </div>
</div>

<!-- Attendance Logs Table -->
<div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm">
    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Attendance Records</h3>
            <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-500 dark:text-gray-400">
                    Showing <span id="showing-count">0</span> of <span id="total-count">0</span> records
                </span>
                <button id="refresh-table" class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-sync-alt"></i>
                </button>
            </div>
        </div>
    </div>
    
    <div class="overflow-x-auto">
        <table class="w-full">
            <thead class="bg-gray-50 dark:bg-gray-700">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Employee
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Date
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Time
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Type
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Status
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Actions
                    </th>
                </tr>
            </thead>
            <tbody id="attendance-table-body" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                <!-- Loading state -->
                <tr>
                    <td colspan="6" class="px-6 py-8 text-center">
                        <div class="flex items-center justify-center">
                            <i class="fas fa-spinner loading-spinner text-gray-400 mr-2"></i>
                            <span class="text-gray-500 dark:text-gray-400">Loading attendance records...</span>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <!-- Pagination -->
    <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-700 dark:text-gray-300">Rows per page:</span>
                <select id="rows-per-page" class="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700 dark:text-white">
                    <option value="10">10</option>
                    <option value="25" selected>25</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                </select>
            </div>
            
            <div class="flex items-center space-x-2">
                <button id="prev-page" class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50" disabled>
                    <i class="fas fa-chevron-left"></i>
                </button>
                <span id="page-info" class="text-sm text-gray-700 dark:text-gray-300">Page 1 of 1</span>
                <button id="next-page" class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50" disabled>
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Attendance page JavaScript
let currentPage = 1;
let rowsPerPage = 25;
let totalRecords = 0;
let currentFilters = {};

// Initialize attendance page
function initAttendancePage() {
    // Set default date range (last 7 days)
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 7);
    
    document.getElementById('start-date').value = startDate.toISOString().split('T')[0];
    document.getElementById('end-date').value = endDate.toISOString().split('T')[0];
    
    loadAttendanceData();
    loadAttendanceStats();
}

// Load attendance data
function loadAttendanceData() {
    const tableBody = document.getElementById('attendance-table-body');
    tableBody.innerHTML = `
        <tr>
            <td colspan="6" class="px-6 py-8 text-center">
                <div class="flex items-center justify-center">
                    <i class="fas fa-spinner loading-spinner text-gray-400 mr-2"></i>
                    <span class="text-gray-500 dark:text-gray-400">Loading attendance records...</span>
                </div>
            </td>
        </tr>
    `;
    
    // Simulate API call
    setTimeout(() => {
        const sampleData = generateSampleAttendanceData();
        displayAttendanceData(sampleData);
        updatePagination();
    }, 1000);
}

// Load attendance statistics
function loadAttendanceStats() {
    // Simulate loading stats
    setTimeout(() => {
        document.getElementById('total-logs').textContent = '156';
        document.getElementById('present-today').textContent = '42';
        document.getElementById('late-arrivals').textContent = '3';
        document.getElementById('early-departures').textContent = '1';
    }, 500);
}

// Generate sample attendance data
function generateSampleAttendanceData() {
    const sampleData = [];
    const names = ['John Doe', 'Jane Smith', 'Mike Johnson', 'Sarah Wilson', 'David Brown'];
    const departments = ['IT', 'HR', 'Finance', 'Operations'];
    
    for (let i = 0; i < 50; i++) {
        const date = new Date();
        date.setDate(date.getDate() - Math.floor(Math.random() * 7));
        
        sampleData.push({
            id: i + 1,
            pin: String(1000 + i).padStart(4, '0'),
            name: names[Math.floor(Math.random() * names.length)],
            department: departments[Math.floor(Math.random() * departments.length)],
            date: date.toISOString().split('T')[0],
            time: `${String(8 + Math.floor(Math.random() * 10)).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`,
            type: Math.random() > 0.5 ? 'Check In' : 'Check Out',
            status: Math.random() > 0.8 ? 'Late' : 'On Time'
        });
    }
    
    return sampleData;
}

// Display attendance data in table
function displayAttendanceData(data) {
    const tableBody = document.getElementById('attendance-table-body');
    
    if (data.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="6" class="px-6 py-8 text-center text-gray-500 dark:text-gray-400">
                    No attendance records found
                </td>
            </tr>
        `;
        return;
    }
    
    const startIndex = (currentPage - 1) * rowsPerPage;
    const endIndex = startIndex + rowsPerPage;
    const pageData = data.slice(startIndex, endIndex);
    
    const rows = pageData.map(record => `
        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mr-3">
                        <i class="fas fa-user text-blue-600 dark:text-blue-400 text-xs"></i>
                    </div>
                    <div>
                        <div class="text-sm font-medium text-gray-900 dark:text-white">${record.name}</div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">PIN: ${record.pin}</div>
                    </div>
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                ${new Date(record.date).toLocaleDateString()}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                ${record.time}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    record.type === 'Check In' 
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                        : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
                }">
                    ${record.type}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    record.status === 'On Time'
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                        : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                }">
                    ${record.status}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <button class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-3">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');
    
    tableBody.innerHTML = rows;
    
    // Update counters
    document.getElementById('showing-count').textContent = pageData.length;
    document.getElementById('total-count').textContent = data.length;
    totalRecords = data.length;
}

// Update pagination controls
function updatePagination() {
    const totalPages = Math.ceil(totalRecords / rowsPerPage);
    
    document.getElementById('page-info').textContent = `Page ${currentPage} of ${totalPages}`;
    document.getElementById('prev-page').disabled = currentPage === 1;
    document.getElementById('next-page').disabled = currentPage === totalPages;
}

// Apply filters
function applyFilters() {
    currentFilters = {
        startDate: document.getElementById('start-date').value,
        endDate: document.getElementById('end-date').value,
        userSearch: document.getElementById('user-search').value,
        department: document.getElementById('department-filter').value
    };
    
    currentPage = 1;
    loadAttendanceData();
    Dashboard.showToast('Filters applied successfully', 'success');
}

// Export attendance data
function exportAttendanceData() {
    Dashboard.showToast('Exporting attendance data...', 'info');
    
    // Simulate export
    setTimeout(() => {
        Dashboard.showToast('Attendance data exported successfully', 'success');
    }, 2000);
}

// Sync attendance data
function syncAttendanceData() {
    Dashboard.showLoading();
    
    // Simulate sync
    setTimeout(() => {
        Dashboard.hideLoading();
        Dashboard.showToast('Attendance data synchronized successfully', 'success');
        loadAttendanceData();
        loadAttendanceStats();
    }, 3000);
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    initAttendancePage();

    // Filter and action buttons
    document.getElementById('apply-filters').addEventListener('click', applyFilters);
    document.getElementById('export-logs').addEventListener('click', exportAttendanceData);
    document.getElementById('sync-attendance').addEventListener('click', syncAttendanceData);
    document.getElementById('refresh-table').addEventListener('click', loadAttendanceData);

    // Pagination
    document.getElementById('prev-page').addEventListener('click', () => {
        if (currentPage > 1) {
            currentPage--;
            loadAttendanceData();
        }
    });

    document.getElementById('next-page').addEventListener('click', () => {
        const totalPages = Math.ceil(totalRecords / rowsPerPage);
        if (currentPage < totalPages) {
            currentPage++;
            loadAttendanceData();
        }
    });

    document.getElementById('rows-per-page').addEventListener('change', (e) => {
        rowsPerPage = parseInt(e.target.value);
        currentPage = 1;
        loadAttendanceData();
    });
});
</script>

<?= $this->endSection() ?>
