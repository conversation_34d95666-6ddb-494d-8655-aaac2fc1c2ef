<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Libraries\FingerspotCurlClient;
use CodeIgniter\HTTP\ResponseInterface;

/**
 * Fingerspot Multiple Days Controller
 * 
 * Comprehensive testing and implementation of multiple days date range handling
 * for the Fingerspot API with automatic chunking
 */
class FingerspotMultipleDaysController extends BaseController
{
    protected FingerspotCurlClient $curlClient;

    public function __construct()
    {
        $this->curlClient = new FingerspotCurlClient();
    }

    /**
     * Test multiple days with detailed chunking information
     */
    public function testMultipleDays(): ResponseInterface
    {
        $startDate = $this->request->getGet('start_date') ?? date('Y-m-d', strtotime('-10 days'));
        $endDate = $this->request->getGet('end_date') ?? date('Y-m-d');
        $transId = $this->request->getGet('trans_id') ?? '1';

        $result = $this->curlClient->getAttlogMultipleDays($transId, $startDate, $endDate, false);

        return $this->response->setJSON([
            'test_type' => 'Multiple Days with Detailed Chunks',
            'description' => 'Tests multiple days date range with detailed chunk information',
            'parameters' => [
                'trans_id' => $transId,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'requested_days' => (new \DateTime($startDate))->diff(new \DateTime($endDate))->days + 1
            ],
            'api_limitation' => 'API allows maximum 2 days per request',
            'chunking_strategy' => 'Automatic 2-day chunks with detailed tracking',
            'result' => $result,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Test multiple days with aggregated data
     */
    public function testAggregated(): ResponseInterface
    {
        $startDate = $this->request->getGet('start_date') ?? date('Y-m-d', strtotime('-7 days'));
        $endDate = $this->request->getGet('end_date') ?? date('Y-m-d');
        $transId = $this->request->getGet('trans_id') ?? '1';

        $result = $this->curlClient->getAttlogMultipleDaysAggregated($transId, $startDate, $endDate);

        return $this->response->setJSON([
            'test_type' => 'Multiple Days Aggregated',
            'description' => 'Tests multiple days with all data combined into single array',
            'parameters' => [
                'trans_id' => $transId,
                'start_date' => $startDate,
                'end_date' => $endDate
            ],
            'result' => $result,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Test monthly attendance data
     */
    public function testMonth(): ResponseInterface
    {
        $month = $this->request->getGet('month') ?? date('Y-m');
        $transId = $this->request->getGet('trans_id') ?? '1';

        $result = $this->curlClient->getAttlogMonth($transId, $month);

        return $this->response->setJSON([
            'test_type' => 'Monthly Attendance',
            'description' => 'Tests full month attendance data retrieval',
            'parameters' => [
                'trans_id' => $transId,
                'month' => $month,
                'month_start' => $month . '-01',
                'month_end' => date('Y-m-t', strtotime($month . '-01'))
            ],
            'result' => $result,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Test weekly attendance data
     */
    public function testWeek(): ResponseInterface
    {
        $weekStart = $this->request->getGet('week_start') ?? date('Y-m-d', strtotime('monday this week'));
        $transId = $this->request->getGet('trans_id') ?? '1';

        $result = $this->curlClient->getAttlogWeek($transId, $weekStart);

        return $this->response->setJSON([
            'test_type' => 'Weekly Attendance',
            'description' => 'Tests full week attendance data retrieval',
            'parameters' => [
                'trans_id' => $transId,
                'week_start' => $weekStart,
                'week_end' => date('Y-m-d', strtotime($weekStart . ' +6 days'))
            ],
            'result' => $result,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Test date range with progress tracking
     */
    public function testDateRange(): ResponseInterface
    {
        $startDate = $this->request->getGet('start_date') ?? date('Y-m-d', strtotime('-15 days'));
        $endDate = $this->request->getGet('end_date') ?? date('Y-m-d');
        $transId = $this->request->getGet('trans_id') ?? '1';

        $progressLog = [];
        
        $result = $this->curlClient->getAttlogDateRange(
            $transId, 
            $startDate, 
            $endDate, 
            function($progress) use (&$progressLog) {
                $progressLog[] = $progress;
            }
        );

        return $this->response->setJSON([
            'test_type' => 'Date Range with Progress',
            'description' => 'Tests date range retrieval with progress tracking',
            'parameters' => [
                'trans_id' => $transId,
                'start_date' => $startDate,
                'end_date' => $endDate
            ],
            'progress_log' => $progressLog,
            'result' => $result,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Test large date range (30 days)
     */
    public function testLargeRange(): ResponseInterface
    {
        $startDate = $this->request->getGet('start_date') ?? date('Y-m-d', strtotime('-30 days'));
        $endDate = $this->request->getGet('end_date') ?? date('Y-m-d');
        $transId = $this->request->getGet('trans_id') ?? '1';

        $startTime = microtime(true);
        $result = $this->curlClient->getAttlogMultipleDaysAggregated($transId, $startDate, $endDate);
        $endTime = microtime(true);
        $executionTime = round($endTime - $startTime, 2);

        return $this->response->setJSON([
            'test_type' => 'Large Date Range (30 days)',
            'description' => 'Tests large date range handling with performance metrics',
            'parameters' => [
                'trans_id' => $transId,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'total_days' => (new \DateTime($startDate))->diff(new \DateTime($endDate))->days + 1
            ],
            'performance' => [
                'execution_time_seconds' => $executionTime,
                'chunks_processed' => $result['chunks_processed'] ?? 0,
                'records_per_second' => $executionTime > 0 ? round(($result['total_records'] ?? 0) / $executionTime, 2) : 0
            ],
            'result' => $result,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Compare single vs multiple days performance
     */
    public function comparePerformance(): ResponseInterface
    {
        $transId = '1';
        
        // Test 1: Single 2-day request
        $startTime1 = microtime(true);
        $singleResult = $this->curlClient->getAttlog($transId, date('Y-m-d', strtotime('-1 day')), date('Y-m-d'));
        $endTime1 = microtime(true);
        $singleTime = round($endTime1 - $startTime1, 2);
        
        // Test 2: Multiple days (7 days)
        $startTime2 = microtime(true);
        $multipleResult = $this->curlClient->getAttlogMultipleDaysAggregated($transId, date('Y-m-d', strtotime('-7 days')), date('Y-m-d'));
        $endTime2 = microtime(true);
        $multipleTime = round($endTime2 - $startTime2, 2);

        return $this->response->setJSON([
            'test_type' => 'Performance Comparison',
            'description' => 'Compares single request vs multiple days performance',
            'single_request' => [
                'date_range' => date('Y-m-d', strtotime('-1 day')) . ' to ' . date('Y-m-d'),
                'execution_time' => $singleTime,
                'records' => is_array(json_decode($singleResult, true)['data'] ?? []) ? count(json_decode($singleResult, true)['data']) : 0,
                'raw_response_size' => strlen($singleResult)
            ],
            'multiple_requests' => [
                'date_range' => date('Y-m-d', strtotime('-7 days')) . ' to ' . date('Y-m-d'),
                'execution_time' => $multipleTime,
                'records' => $multipleResult['total_records'] ?? 0,
                'chunks_processed' => $multipleResult['chunks_processed'] ?? 0,
                'overhead_factor' => $singleTime > 0 ? round($multipleTime / $singleTime, 2) : 0
            ],
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Test all multiple days functionality
     */
    public function testAll(): ResponseInterface
    {
        $tests = [];
        
        // Test 1: 7 days aggregated
        $tests['7_days_aggregated'] = $this->curlClient->getAttlogMultipleDaysAggregated('1', date('Y-m-d', strtotime('-7 days')), date('Y-m-d'));
        
        // Test 2: Current month
        $tests['current_month'] = $this->curlClient->getAttlogMonth('1');
        
        // Test 3: Current week
        $tests['current_week'] = $this->curlClient->getAttlogWeek('1');
        
        // Test 4: 15 days with detailed chunks
        $tests['15_days_detailed'] = $this->curlClient->getAttlogMultipleDays('1', date('Y-m-d', strtotime('-15 days')), date('Y-m-d'), false);

        return $this->response->setJSON([
            'test_type' => 'Complete Multiple Days Test Suite',
            'description' => 'Tests all multiple days functionality',
            'tests' => $tests,
            'summary' => [
                '7_days_records' => $tests['7_days_aggregated']['total_records'] ?? 0,
                'month_records' => $tests['current_month']['total_records'] ?? 0,
                'week_records' => $tests['current_week']['total_records'] ?? 0,
                '15_days_chunks' => $tests['15_days_detailed']['total_chunks'] ?? 0,
                'total_api_calls' => ($tests['7_days_aggregated']['chunks_processed'] ?? 0) + 
                                   ($tests['current_month']['chunks_processed'] ?? 0) + 
                                   ($tests['current_week']['chunks_processed'] ?? 0) + 
                                   ($tests['15_days_detailed']['total_chunks'] ?? 0)
            ],
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Get configuration and capabilities
     */
    public function capabilities(): ResponseInterface
    {
        return $this->response->setJSON([
            'multiple_days_capabilities' => [
                'max_single_request_days' => 2,
                'automatic_chunking' => true,
                'aggregated_data_support' => true,
                'progress_tracking' => true,
                'monthly_data_support' => true,
                'weekly_data_support' => true,
                'large_range_support' => true,
                'performance_optimization' => true
            ],
            'available_methods' => [
                'getAttlogMultipleDays' => 'Detailed chunking with full information',
                'getAttlogMultipleDaysAggregated' => 'Simplified response with combined data',
                'getAttlogMonth' => 'Full month data retrieval',
                'getAttlogWeek' => 'Full week data retrieval',
                'getAttlogDateRange' => 'Date range with progress callback'
            ],
            'api_limitations' => [
                'max_date_range_per_request' => '2 days',
                'chunking_required_for' => 'Date ranges > 2 days',
                'recommended_max_range' => '30 days for optimal performance'
            ],
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
}
