<?php

namespace App\Libraries;

/**
 * Fingerspot cURL Client
 * 
 * This class implements the exact cURL code from the PDF documentation
 * using environment variables for API token and cloud_id
 */
class FingerspotCurlClient
{
    protected string $apiToken;
    protected string $cloudId;
    protected string $baseUrl;

    public function __construct()
    {
        // Load from environment variables
        $this->apiToken = env('fingerspot.apiToken', '4FWPFC5UR2M4Y7G6');
        $this->cloudId = env('fingerspot.cloudId', 'C2630450C3233D26');
        $this->baseUrl = env('fingerspot.baseUrl', 'https://developer.fingerspot.io/api');
    }

    /**
     * Get Attlog - Exact implementation from PDF documentation
     * 
     * @param string $transId Transaction ID
     * @param string $startDate Start date (Y-m-d format)
     * @param string $endDate End date (Y-m-d format)
     * @return string Raw response from API
     */
    public function getAttlog(string $transId = "1", string $startDate = null, string $endDate = null): string
    {
        // Default dates if not provided - API allows max 2 days range
        if ($startDate === null) {
            $startDate = date('Y-m-d', strtotime('-1 day')); // Yesterday
        }
        if ($endDate === null) {
            $endDate = date('Y-m-d'); // Today
        }

        // Validate date range - API limitation: max 2 days
        $start = new \DateTime($startDate);
        $end = new \DateTime($endDate);
        $diff = $start->diff($end)->days;

        if ($diff > 2) {
            // Adjust end date to be max 2 days from start date
            $endDate = $start->add(new \DateInterval('P2D'))->format('Y-m-d');
        }

        // Exact URL from PDF documentation
        $url = 'https://developer.fingerspot.io/api/get_attlog';

        // Exact data format from PDF documentation
        $data = json_encode([
            "trans_id" => $transId,
            "cloud_id" => $this->cloudId,
            "start_date" => $startDate,
            "end_date" => $endDate
        ]);
        
        // Exact authorization header from PDF documentation
        $authorization = "Authorization: Bearer " . $this->apiToken;
        
        // Exact cURL implementation from PDF documentation
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json',
            $authorization
        ));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        
        $result = curl_exec($ch);
        curl_close($ch);
        
        return $result;
    }

    /**
     * Get Attlog with current date range (respecting 2-day API limit)
     *
     * @param string $transId Transaction ID
     * @param int $daysBack Number of days back from today (max 2 due to API limit)
     * @return string Raw response from API
     */
    public function getAttlogCurrent(string $transId = "1", int $daysBack = 2): string
    {
        // Limit to max 2 days due to API constraint
        $daysBack = min($daysBack, 2);

        $endDate = date('Y-m-d');
        $startDate = date('Y-m-d', strtotime("-{$daysBack} days"));

        return $this->getAttlog($transId, $startDate, $endDate);
    }

    /**
     * Get Attlog for multiple days (handles API 2-day limit by making multiple requests)
     *
     * @param string $transId Transaction ID
     * @param string $startDate Start date (Y-m-d format)
     * @param string $endDate End date (Y-m-d format)
     * @param bool $aggregateData Whether to combine all data into single array
     * @return array Combined results from multiple API calls
     */
    public function getAttlogMultipleDays(string $transId = "1", string $startDate = null, string $endDate = null, bool $aggregateData = false): array
    {
        if ($startDate === null) {
            $startDate = date('Y-m-d', strtotime('-7 days'));
        }
        if ($endDate === null) {
            $endDate = date('Y-m-d');
        }

        $start = new \DateTime($startDate);
        $end = new \DateTime($endDate);
        $results = [];
        $allData = [];
        $totalRecords = 0;
        $successfulChunks = 0;
        $failedChunks = 0;

        while ($start <= $end) {
            // Calculate chunk end date (max 2 days from start)
            $chunkEnd = clone $start;
            $chunkEnd->add(new \DateInterval('P2D'));

            // Don't exceed the requested end date
            if ($chunkEnd > $end) {
                $chunkEnd = $end;
            }

            // Make API call for this chunk
            $chunkResult = $this->getAttlog(
                $transId,
                $start->format('Y-m-d'),
                $chunkEnd->format('Y-m-d')
            );

            $parsedResponse = json_decode($chunkResult, true);
            $isSuccess = $parsedResponse && isset($parsedResponse['success']) && $parsedResponse['success'];

            if ($isSuccess) {
                $successfulChunks++;
                if (isset($parsedResponse['data']) && is_array($parsedResponse['data'])) {
                    $chunkRecords = count($parsedResponse['data']);
                    $totalRecords += $chunkRecords;

                    if ($aggregateData) {
                        $allData = array_merge($allData, $parsedResponse['data']);
                    }
                } else {
                    $chunkRecords = 0;
                }
            } else {
                $failedChunks++;
                $chunkRecords = 0;
            }

            $results[] = [
                'chunk_number' => count($results) + 1,
                'date_range' => $start->format('Y-m-d') . ' to ' . $chunkEnd->format('Y-m-d'),
                'start_date' => $start->format('Y-m-d'),
                'end_date' => $chunkEnd->format('Y-m-d'),
                'days_in_chunk' => $start->diff($chunkEnd)->days + 1,
                'success' => $isSuccess,
                'records_count' => $chunkRecords,
                'raw_response' => $chunkResult,
                'parsed_response' => $parsedResponse
            ];

            // Move to next chunk (start from day after chunk end)
            $start = clone $chunkEnd;
            $start->add(new \DateInterval('P1D'));
        }

        $response = [
            'success' => $successfulChunks > 0,
            'total_chunks' => count($results),
            'successful_chunks' => $successfulChunks,
            'failed_chunks' => $failedChunks,
            'total_records' => $totalRecords,
            'requested_range' => [
                'start_date' => $startDate,
                'end_date' => $endDate,
                'total_days' => (new \DateTime($startDate))->diff(new \DateTime($endDate))->days + 1
            ],
            'chunks' => $results
        ];

        if ($aggregateData && !empty($allData)) {
            $response['aggregated_data'] = $allData;
            $response['data'] = $allData; // For compatibility
        }

        return $response;
    }

    /**
     * Get Attlog for multiple days with aggregated data (simplified response)
     *
     * @param string $transId Transaction ID
     * @param string $startDate Start date (Y-m-d format)
     * @param string $endDate End date (Y-m-d format)
     * @return array Simplified response with all data combined
     */
    public function getAttlogMultipleDaysAggregated(string $transId = "1", string $startDate = null, string $endDate = null): array
    {
        $result = $this->getAttlogMultipleDays($transId, $startDate, $endDate, true);

        return [
            'success' => $result['success'],
            'message' => $result['success'] ? 'Successfully retrieved attendance logs' : 'Some requests failed',
            'total_records' => $result['total_records'],
            'date_range' => $result['requested_range'],
            'chunks_processed' => $result['total_chunks'],
            'successful_chunks' => $result['successful_chunks'],
            'failed_chunks' => $result['failed_chunks'],
            'data' => $result['aggregated_data'] ?? []
        ];
    }

    /**
     * Get Attlog for a specific month
     *
     * @param string $transId Transaction ID
     * @param string $month Month in Y-m format (e.g., "2024-01")
     * @return array Monthly attendance data
     */
    public function getAttlogMonth(string $transId = "1", string $month = null): array
    {
        if ($month === null) {
            $month = date('Y-m');
        }

        $startDate = $month . '-01';
        $endDate = date('Y-m-t', strtotime($startDate)); // Last day of month

        return $this->getAttlogMultipleDaysAggregated($transId, $startDate, $endDate);
    }

    /**
     * Get Attlog for a specific week
     *
     * @param string $transId Transaction ID
     * @param string $weekStartDate Start date of week (Y-m-d format)
     * @return array Weekly attendance data
     */
    public function getAttlogWeek(string $transId = "1", string $weekStartDate = null): array
    {
        if ($weekStartDate === null) {
            // Start of current week (Monday)
            $weekStartDate = date('Y-m-d', strtotime('monday this week'));
        }

        $endDate = date('Y-m-d', strtotime($weekStartDate . ' +6 days'));

        return $this->getAttlogMultipleDaysAggregated($transId, $weekStartDate, $endDate);
    }

    /**
     * Get Attlog for date range with automatic chunking and progress callback
     *
     * @param string $transId Transaction ID
     * @param string $startDate Start date (Y-m-d format)
     * @param string $endDate End date (Y-m-d format)
     * @param callable|null $progressCallback Callback function for progress updates
     * @return array Attendance data with progress information
     */
    public function getAttlogDateRange(string $transId = "1", string $startDate = null, string $endDate = null, ?callable $progressCallback = null): array
    {
        if ($startDate === null) {
            $startDate = date('Y-m-d', strtotime('-30 days'));
        }
        if ($endDate === null) {
            $endDate = date('Y-m-d');
        }

        $start = new \DateTime($startDate);
        $end = new \DateTime($endDate);
        $totalDays = $start->diff($end)->days + 1;
        $estimatedChunks = ceil($totalDays / 2);

        $results = [];
        $allData = [];
        $processedChunks = 0;
        $totalRecords = 0;

        while ($start <= $end) {
            $chunkEnd = clone $start;
            $chunkEnd->add(new \DateInterval('P2D'));

            if ($chunkEnd > $end) {
                $chunkEnd = $end;
            }

            $chunkResult = $this->getAttlog(
                $transId,
                $start->format('Y-m-d'),
                $chunkEnd->format('Y-m-d')
            );

            $parsedResponse = json_decode($chunkResult, true);
            $isSuccess = $parsedResponse && isset($parsedResponse['success']) && $parsedResponse['success'];

            if ($isSuccess && isset($parsedResponse['data']) && is_array($parsedResponse['data'])) {
                $allData = array_merge($allData, $parsedResponse['data']);
                $totalRecords += count($parsedResponse['data']);
            }

            $processedChunks++;

            // Call progress callback if provided
            if ($progressCallback && is_callable($progressCallback)) {
                $progressCallback([
                    'processed_chunks' => $processedChunks,
                    'total_chunks' => $estimatedChunks,
                    'current_range' => $start->format('Y-m-d') . ' to ' . $chunkEnd->format('Y-m-d'),
                    'records_so_far' => $totalRecords,
                    'progress_percentage' => round(($processedChunks / $estimatedChunks) * 100, 2)
                ]);
            }

            $start = clone $chunkEnd;
            $start->add(new \DateInterval('P1D'));
        }

        return [
            'success' => $totalRecords > 0,
            'message' => "Retrieved {$totalRecords} records from {$processedChunks} API calls",
            'total_records' => $totalRecords,
            'chunks_processed' => $processedChunks,
            'date_range' => [
                'start_date' => $startDate,
                'end_date' => $endDate,
                'total_days' => $totalDays
            ],
            'data' => $allData
        ];
    }

    /**
     * Get Attlog with parsed JSON response
     * 
     * @param string $transId Transaction ID
     * @param string $startDate Start date (Y-m-d format)
     * @param string $endDate End date (Y-m-d format)
     * @return array Parsed JSON response
     */
    public function getAttlogParsed(string $transId = "1", string $startDate = null, string $endDate = null): array
    {
        $result = $this->getAttlog($transId, $startDate, $endDate);
        $parsed = json_decode($result, true);
        
        return [
            'raw_response' => $result,
            'parsed_data' => $parsed,
            'success' => $parsed !== null,
            'error' => json_last_error_msg()
        ];
    }

    /**
     * Generic cURL request method following PDF documentation pattern
     * 
     * @param string $endpoint API endpoint
     * @param array $data Request data
     * @param string $method HTTP method
     * @return string Raw response
     */
    public function makeRequest(string $endpoint, array $data = [], string $method = 'POST'): string
    {
        $url = rtrim($this->baseUrl, '/') . '/' . ltrim($endpoint, '/');
        
        // Prepare data
        $jsonData = json_encode($data);
        
        // Authorization header
        $authorization = "Authorization: Bearer " . $this->apiToken;
        
        // Initialize cURL following PDF documentation pattern
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json',
            $authorization
        ));
        
        if (strtoupper($method) === 'POST') {
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
        } elseif (strtoupper($method) === 'PUT') {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
            curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
        } elseif (strtoupper($method) === 'DELETE') {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
            curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
        }
        
        $result = curl_exec($ch);
        curl_close($ch);
        
        return $result;
    }

    /**
     * Get device info using PDF documentation pattern
     * 
     * @return string Raw response
     */
    public function getDeviceInfo(): string
    {
        return $this->makeRequest('get_device_info', [
            'cloud_id' => $this->cloudId
        ]);
    }

    /**
     * Get user info using PDF documentation pattern
     * 
     * @param string|null $userPin Specific user PIN or null for all users
     * @return string Raw response
     */
    public function getUserInfo(?string $userPin = null): string
    {
        $data = ['cloud_id' => $this->cloudId];
        if ($userPin) {
            $data['user_pin'] = $userPin;
        }
        
        return $this->makeRequest('get_userinfo', $data);
    }

    /**
     * Set user info using PDF documentation pattern
     * 
     * @param array $userData User data
     * @return string Raw response
     */
    public function setUserInfo(array $userData): string
    {
        $data = array_merge(['cloud_id' => $this->cloudId], $userData);
        return $this->makeRequest('set_userinfo', $data);
    }

    /**
     * Set device time using PDF documentation pattern
     * 
     * @param string|null $datetime Datetime string or null for current time
     * @return string Raw response
     */
    public function setTime(?string $datetime = null): string
    {
        $data = [
            'cloud_id' => $this->cloudId,
            'datetime' => $datetime ?: date('Y-m-d H:i:s')
        ];
        
        return $this->makeRequest('set_time', $data);
    }

    /**
     * Restart device using PDF documentation pattern
     * 
     * @return string Raw response
     */
    public function restartDevice(): string
    {
        return $this->makeRequest('restart_device', [
            'cloud_id' => $this->cloudId
        ]);
    }

    /**
     * Register online using PDF documentation pattern
     * 
     * @param array $registerData Registration data
     * @return string Raw response
     */
    public function registerOnline(array $registerData): string
    {
        $data = array_merge(['cloud_id' => $this->cloudId], $registerData);
        return $this->makeRequest('register_online', $data);
    }

    /**
     * Delete user using PDF documentation pattern
     * 
     * @param string $userPin User PIN to delete
     * @return string Raw response
     */
    public function deleteUser(string $userPin): string
    {
        return $this->makeRequest('delete_user', [
            'cloud_id' => $this->cloudId,
            'user_pin' => $userPin
        ]);
    }

    /**
     * Get current configuration
     * 
     * @return array Configuration details
     */
    public function getConfig(): array
    {
        return [
            'api_token' => substr($this->apiToken, 0, 8) . '...',
            'cloud_id' => $this->cloudId,
            'base_url' => $this->baseUrl
        ];
    }
}
