<?= $this->extend('dashboard/layout') ?>

<?= $this->section('content') ?>

<!-- <PERSON> Header -->
<div class="mb-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Settings</h1>
            <p class="text-gray-600 dark:text-gray-400">Configure your Fingerspot system settings</p>
        </div>
        <div class="flex items-center space-x-3">
            <button id="backup-settings" class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                <i class="fas fa-download mr-2"></i>
                Backup Settings
            </button>
            <button id="save-all-settings" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-save mr-2"></i>
                Save All
            </button>
        </div>
    </div>
</div>

<!-- Settings Tabs -->
<div class="mb-6">
    <div class="border-b border-gray-200 dark:border-gray-700">
        <nav class="-mb-px flex space-x-8">
            <button class="settings-tab active" data-tab="general">
                <i class="fas fa-cog mr-2"></i>
                General
            </button>
            <button class="settings-tab" data-tab="device">
                <i class="fas fa-microchip mr-2"></i>
                Device
            </button>
            <button class="settings-tab" data-tab="notifications">
                <i class="fas fa-bell mr-2"></i>
                Notifications
            </button>
            <button class="settings-tab" data-tab="security">
                <i class="fas fa-shield-alt mr-2"></i>
                Security
            </button>
            <button class="settings-tab" data-tab="backup">
                <i class="fas fa-database mr-2"></i>
                Backup
            </button>
        </nav>
    </div>
</div>

<!-- General Settings Tab -->
<div id="general-tab" class="settings-content">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- System Configuration -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">System Configuration</h3>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">System Name</label>
                    <input type="text" id="system-name" value="Fingerspot Management System" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Time Zone</label>
                    <select id="timezone" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                        <option value="Asia/Jakarta">Asia/Jakarta (WIB)</option>
                        <option value="Asia/Makassar">Asia/Makassar (WITA)</option>
                        <option value="Asia/Jayapura">Asia/Jayapura (WIT)</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Date Format</label>
                    <select id="date-format" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                        <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                        <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                        <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Language</label>
                    <select id="language" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                        <option value="en">English</option>
                        <option value="id">Bahasa Indonesia</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Working Hours -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Working Hours</h3>
            <div class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Start Time</label>
                        <input type="time" id="work-start" value="08:00" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">End Time</label>
                        <input type="time" id="work-end" value="17:00" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                    </div>
                </div>

                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Break Start</label>
                        <input type="time" id="break-start" value="12:00" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Break End</label>
                        <input type="time" id="break-end" value="13:00" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Late Tolerance (minutes)</label>
                    <input type="number" id="late-tolerance" value="15" min="0" max="60" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                </div>

                <div>
                    <label class="flex items-center">
                        <input type="checkbox" id="weekend-work" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Allow weekend work</span>
                    </label>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Device Settings Tab -->
<div id="device-tab" class="settings-content hidden">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- API Configuration -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">API Configuration</h3>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Base URL</label>
                    <input type="url" id="api-base-url" value="https://developer.fingerspot.io/api" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">API Token</label>
                    <div class="relative">
                        <input type="password" id="api-token" value="4FWPFC5UR2M4Y7G6" class="w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                        <button type="button" onclick="togglePassword('api-token')" class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <i class="fas fa-eye text-gray-400"></i>
                        </button>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Cloud ID</label>
                    <input type="text" id="cloud-id" value="C2630450C3233D26" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Connection Timeout (seconds)</label>
                    <input type="number" id="connection-timeout" value="30" min="5" max="120" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                </div>

                <button onclick="testApiConnection()" class="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                    <i class="fas fa-plug mr-2"></i>
                    Test Connection
                </button>
            </div>
        </div>

        <!-- Sync Settings -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Synchronization Settings</h3>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Auto Sync Interval</label>
                    <select id="sync-interval" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                        <option value="5">Every 5 minutes</option>
                        <option value="15">Every 15 minutes</option>
                        <option value="30" selected>Every 30 minutes</option>
                        <option value="60">Every hour</option>
                        <option value="0">Manual only</option>
                    </select>
                </div>

                <div>
                    <label class="flex items-center">
                        <input type="checkbox" id="auto-sync" checked class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Enable automatic synchronization</span>
                    </label>
                </div>

                <div>
                    <label class="flex items-center">
                        <input type="checkbox" id="sync-on-startup" checked class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Sync on system startup</span>
                    </label>
                </div>

                <div>
                    <label class="flex items-center">
                        <input type="checkbox" id="retry-failed-sync" checked class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Retry failed synchronizations</span>
                    </label>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Max Retry Attempts</label>
                    <input type="number" id="max-retries" value="3" min="1" max="10" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                </div>

                <div class="pt-4 border-t border-gray-200 dark:border-gray-700">
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-600 dark:text-gray-400">Last Sync:</span>
                        <span id="last-sync-time" class="text-gray-900 dark:text-white">2 minutes ago</span>
                    </div>
                    <div class="flex justify-between text-sm mt-1">
                        <span class="text-gray-600 dark:text-gray-400">Next Sync:</span>
                        <span id="next-sync-time" class="text-gray-900 dark:text-white">28 minutes</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>